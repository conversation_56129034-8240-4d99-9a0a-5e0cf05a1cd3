Machine learning - Wikipedia
Machine learning Classification Clustering Regression Anomaly detection Association rules Reinforcement learning Structured prediction Feature engineering Feature learning Online learning Semi-supervised learning Unsupervised learning Learning to rank Grammar induction Decision trees Linear regression Naive Bayes Neural networks Logistic regression Perceptron Relevance vector machine (RVM) Support vector machine (SVM) BIRCH Hierarchical Expectation-maximization (EM) OPTICS Mean-shift Factor analysis CCA ICA LDA NMF PCA t-SNE Local outlier factor Autoencoder Deep learning Multilayer perceptron RNN Restricted Boltzmann machine SOM Convolutional neural network Q-Learning SARSA Temporal Difference (TD) Bias-variance dilemma Computational learning theory Empirical risk minimization Occam learning PAC learning Statistical learning VC theory NIPS ICML ML JMLR ArXiv:cs.LG List of datasets for machine learning research Outline of machine learning v t e verify Contents 1 Overview 1.1 Types of problems and tasks 2 History and relationships to other fields 2.1 Relation to statistics 3 Theory 4 Approaches 4.1 Decision tree learning 4.2 Association rule learning 4.3 Artificial neural networks 4.4 Deep learning 4.5 Inductive logic programming 4.6 Support vector machines 4.7 Clustering 4.8 Bayesian networks 4.9 Reinforcement learning 4.10 Representation learning 4.11 Similarity and metric learning 4.12 Sparse dictionary learning 4.13 Genetic algorithms 4.14 Rule-based machine learning 4.15 Learning classifier systems 5 Applications 6 Model assessments 7 Ethics 8 Software 8.1 Free and open-source software 8.2 Proprietary software with free and open-source editions 8.3 Proprietary software 9 Journals 10 Conferences 11 See also 12 References 13 Further reading 14 External links Overview [ ] Types of problems and tasks [ ] History and relationships to other fields [ ] Relation to statistics [ ] [ ] Approaches [ ] Decision tree learning [ ] Association rule learning [ ] Association rule learning is a method for discovering interesting relations between variables in large databases. Artificial neural networks [ ] Deep learning [ ] Inductive logic programming [ ] Support vector machines [ ] Clustering [ ] Bayesian networks [ ] Reinforcement learning [ ] Representation learning [ ] Similarity and metric learning [ ] Sparse dictionary learning [ ] Genetic algorithms [ ] Rule-based machine learning [ ] Learning classifier systems [ ] Applications [ ] Applications for machine learning include: citation needed Affective computing Bioinformatics Brain–machine interfaces Cheminformatics Computational anatomy Information retrieval Linguistics Marketing Machine learning control Machine perception Medical diagnosis Economics Insurance Natural language processing Online advertising Recommender systems Robot locomotion Search engines Sequence mining Software engineering Structural health monitoring Syntactic pattern recognition Time series forecasting User behavior analytics Model assessments [ ] Ethics [ ] Software [ ] Free and open-source software [ ] CNTK Deeplearning4j dlib ELKI GNU Octave H2O Mahout Mallet mlpy MLPACK MOA (Massive Online Analysis) MXNet ND4J: ND arrays for Java NuPIC OpenAI Gym OpenAI Universe OpenNN Orange R scikit-learn Shogun TensorFlow Torch Yooreeka Weka Proprietary software with free and open-source editions [ ] KNIME RapidMiner Proprietary software [ ] Amazon Machine Learning Ayasdi IBM Data Science Experience Google Prediction API IBM SPSS Modeler KXEN Modeler LIONsolver Mathematica MATLAB Microsoft Azure Machine Learning Neural Designer NeuroSolutions Oracle Data Mining RCASE SAS Enterprise Miner SequenceL Skymind Splunk Journals [ ] Journal of Machine Learning Research Machine Learning Neural Computation Conferences [ ] Conference on Neural Information Processing Systems International Conference on Machine Learning International Conference on Learning Representations See also [ ] Artificial intelligence portal Machine learning portal Artificial intelligence Automatic reasoning Big data Computational intelligence Computational neuroscience Data science Deep learning Ethics of artificial intelligence Existential risk from advanced artificial intelligence Explanation-based learning Quantum machine learning Important publications in machine learning List of machine learning algorithms List of datasets for machine learning research Similarity learning Machine-learning applications in bioinformatics References [ ] ^     ^     ^     ^ R. Kohavi and F. Provost, \Glossary of terms," Machine Learning, vol. 30, no. 2-3, pp. 271-274, 1998. ^ ^     Machine learning and pattern recognition "can be viewed as two facets of the same field." ^ 2017-05-23     ^ ^     ^     ^     ^     ^ 2016-03-29     ^ 2017-04-10     ^ 2017-04-10     ^ 2017-04-10     ^     ^         ^         ^     2014-10-01     ^ 8 August     ^     ^     ^     4 February     ^ ^ (PDF)     ^     ^ ^ Aharon, M, M Elad, and A Bruckstein. 2006. "K-SVD: An Algorithm for Designing Overcomplete Dictionaries for Sparse Representation." Signal Processing, IEEE Transactions on 54 (11): 4311–4322 ^     ^     ^ (PDF)     ^     ^     ^ ^ ^ ^ ^ 4 Mar     ^ ^ 8 August     ^ [1] ^     ^ ^ (PDF)     ^ (PDF) 11 April     ^ [2] Further reading [ ] External links [ ] International Machine Learning Society Machine learning Cybernetics Learning All pages needing factual verification Wikipedia articles needing factual verification from August 2017 Articles containing potentially dated statements from 2016 All articles containing potentially dated statements All articles with unsourced statements Articles with unsourced statements from August 2017 Navigation menu Personal tools Not logged in Talk Contributions Create account Log in Namespaces Article Article Talk Talk Variants
Views Read Read Edit Edit View history View history More More
Navigation Main page Contents Featured content Current events Random article Donate to Wikipedia Wikipedia store Interaction Help About Wikipedia Community portal Recent changes Contact page Tools What links here Related changes Upload file Special pages Permanent link Page information Wikidata item Cite this page Print/export Create a book Download as PDF Printable version In other projects Wikimedia Commons Languages العربية অসমীয়া Azərbaycanca Български Català Čeština Dansk Deutsch Eesti Ελληνικά Español Euskara فارسی Français 한국어 Հայերեն हिन्दी Bahasa Indonesia Íslenska Italiano עברית ಕನ್ನಡ Latviešu Lietuvių Magyar Македонски മലയാളം मराठी Nederlands 日本語 Norsk Norsk nynorsk Polski Português Русский Shqip Simple English Slovenščina Српски / srpski Srpskohrvatski / српскохрватски Suomi Svenska Tagalog தமிழ் ไทย Türkçe Українська Tiếng Việt 中文 Edit links
This page was last edited on 17 September 2017, at 13:48. Privacy policy About Wikipedia Disclaimers Contact Wikipedia Developers Cookie statement Mobile view