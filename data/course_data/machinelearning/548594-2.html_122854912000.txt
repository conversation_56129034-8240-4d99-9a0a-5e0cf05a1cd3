Machine Learning & AI Foundations: Value Estimations
Toggle Navigation Lynda.com from LinkedIn 3D + Animation See All Topics See All 3D + Animation See All Software See All 3D + Animation See All Learning Paths See All Audio + Music See All Topics See All Audio + Music See All Software See All Audio + Music See All Learning Paths See All Business See All Topics See All Business See All Software See All Business See All Learning Paths See All Business Starting a Business Becoming a Manager CAD See All Topics See All CAD See All Software See All CAD See All Learning Paths See All Design See All Topics See All Design See All Software See All Design See All Learning Paths See All Developer See All Topics See All Developer See All Software See All Developer See All Learning Paths See All Education + Elearning See All Topics See All Education + Elearning See All Software See All Education + Elearning See All Learning Paths See All IT See All Topics See All IT See All Software See All IT See All Learning Paths See All Marketing See All Topics See All Marketing See All Software See All Marketing See All Learning Paths See All Photography See All Topics See All Photography See All Software See All Photography See All Learning Paths See All Video See All Topics See All Video See All Software See All Video See All Learning Paths See All Web See All Topics See All Web See All Software See All Web See All Learning Paths See All Web Starting a Career in UX Design Clear Search Search
Solutions for: &vert; Toggle search bar Close this alert message IT Big Data Machine Learning & AI Foundations: Value Estimations Share Keyboard Shortcuts
Keyboard Shortcuts
Course
3/22/2017 Setting up the development environment Building a simple home value estimator Finding the best weights automatically Working with large data sets efficiently Training a supervised machine learning model Exploring a home value data set Deciding how much data is needed Preparing the features Training the value estimator Measuring accuracy with mean absolute error Improving a system Using the machine learning model to make predictions 1h 4m 99,285 Show More Show Less - [Adam] How do websites automatically determine how much a house is worth? This is an example of using machine learning for value estimation. A machine learning model uses information from other homes sold in the area and produces a value estimate for a different house. Hi, I'm Adam Geitgey and I'd like to welcome you to this course where you'll build a value estimation system that can automatically deduce the value of your house based on its location and characteristics. First, we'll cover how to use training data to build a machine learning model. Then we'll explore how to use that machine learning model in your own programs. But the skills you'll develop in this course aren't limited to real estate. You can use the exact same approach to solve any kind of value estimation problem with machine learning. Let's dive in. Related Courses Preview course Data Science Foundations: Fundamentals 3h 6m Beginner Preview course Learning Data Science: Understanding the Basics 1h 16m Appropriate for all Preview course Machine Learning & AI Foundations: Decision Trees 1h 16m Beginner Preview course Machine Learning & AI: Advanced Decision Trees 1h 16m Advanced Clear Search Search 42s 21s 31s 2m 21s 3m 11s 2m 45s 2m 37s 4m 7s 2m 6s 1m 22s 2m 58s 2m 22s 3m 55s 2m 56s 53s 2m 4s 4m 11s 3m 19s 1m 50s 1m 48s 1m 3s 2m 51s 1m 31s 2m 44s 2m 46s 2m 26s 2m 39s 1m 48s 48s Show More Show Less Mark as unwatched Mark as unwatched Mark all as unwatched Mark all as unwatched Are you sure you want to mark all the videos in this course as unwatched? This will not affect your course history, your reports, or your certificates of completion for this course. Cancel Take notes with your new membership! 1:30 1:30   Start Your Free Trial Now You started this assessment previously and didn't complete it. You can pick up where you left off, or start over. Share this video Embed this video Video: Welcome This movie is locked and only viewable to logged-in members. Embed the preview of this course instead. About Us Products Support Apps Connect RSS feed Follow us on Twitter Follow us on LinkedIn Follow us on Facebook Follow us on Google+ © 2017 LinkedIn Corporation Site Map Privacy policy Web Use Policy Cookie Policy Lynda.com | from LinkedIn Go to top of page × Thank you for taking the time to let us know what you think of our site. We were unable to submit your feedback. Try again