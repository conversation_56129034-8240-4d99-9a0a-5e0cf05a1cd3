Computer vision - Wikipedia
Computer vision Contents 1 Definition 2 History 3 Related fields 4 Applications 5 Typical tasks 5.1 Recognition 5.2 Motion analysis 5.3 Scene reconstruction 5.4 Image restoration 6 System methods 6.1 Image-understanding systems 7 Hardware 8 See also 8.1 Lists 9 References 10 Further reading 11 External links Definition [ ] History [ ] Related fields [ ] The following characterizations appear relevant but should not be taken as universally accepted: Applications [ ] Play media Play media Other application areas include: Typical tasks [ ] Each of the application areas described above employ a range of computer vision tasks; more or less well-defined measurement problems or processing problems, which can be solved using a variety of methods. Some examples of typical computer vision tasks are presented below. Recognition [ ] The classical problem in computer vision, image processing, and machine vision is that of determining whether or not the image data contains some specific object, feature, or activity. Different varieties of the recognition problem are described in the literature: Several specialized tasks based on recognition exist, such as: Facial recognition Motion analysis [ ] Several tasks relate to motion estimation where an image sequence is processed to produce an estimate of the velocity either at each points in the image or in the 3D scene, or even of the camera that produces the images . Examples of such tasks are: Scene reconstruction [ ] Image restoration [ ] The aim of image restoration is the removal of noise (sensor noise, motion blur, etc.) from images. The simplest possible approach for noise removal is various types of filters such as low-pass filters or median filters. More sophisticated methods assume a model of how the local image structures look like, a model which distinguishes them from the noise. By first analysing the image data in terms of the local image structures, such as lines or edges, and then controlling the filtering based on local information from the analysis step, a better level of noise removal is usually obtained compared to the simpler approaches. System methods [ ] The organization of a computer vision system is highly application dependent. Some systems are stand-alone applications which solve a specific measurement or detection problem, while others constitute a sub-system of a larger design which, for example, also contains sub-systems for control of mechanical actuators, planning, information databases, man-machine interfaces, etc. The specific implementation of a computer vision system also depends on if its functionality is pre-specified or if some part of it can be learned or modified during operation. Many functions are unique to the application. There are, however, typical functions which are found in many computer vision systems. Re-sampling in order to assure that the image coordinate system is correct. Noise reduction in order to assure that sensor noise does not introduce false information. Contrast enhancement to assure that relevant information can be detected. Selection of a specific set of interest points Segmentation of one or multiple image regions which contain a specific object of interest. Verification that the data satisfy model-based and application specific assumptions. Estimation of application specific parameters, such as object pose or object size. Pass/fail on automatic inspection applications Match / no-match in recognition applications Flag for further human review in medical, military, security and recognition applications Image-understanding systems [ ] Image-understanding systems (IUS) include three levels of abstraction as follows: Low level includes image primitives such as edges, texture elements, or regions; intermediate level includes boundaries, surfaces and volumes; and high level includes objects, scenes, or events. Many of these requirements are really topics for further research. The representational requirements in the designing of IUS for these levels are: representation of prototypical concepts, concept organization, spatial knowledge, temporal knowledge, scaling, and description by comparison and differentiation. Hardware [ ] There are many kinds of computer vision systems, nevertheless all of them contain these basic elements: a power source, at least one image acquisition device (i.e. camera, ccd, etc.), a processor as well as control and communication cables or some kind of wireless interconnection mechanism. In addition, a practical vision system contains software, as well as a display in order to monitor the system. Vision systems for inner spaces, as most industrial ones, contain an illumination system and may be placed in a controlled environment. Furthermore, a completed system includes many accessories like camera supports, cables and connectors. Most computer vision systems use visible-light cameras passively viewing a scene at frame rates of at most 60 frames per second (usually far slower). See also [ ] AI effect Applications of artificial intelligence Machine vision glossary Space mapping Teknomo-Fernandez Algorithm Visual system Visual perception Vision science Lists [ ] List of computer vision topics List of emerging technologies Outline of artificial intelligence References [ ]     (PDF)                             ^ ^         ^ 2 August     ^     ^     ^     ^     ^     ^ 2010-11-05     ^     ^     ^ Barghout, Lauren. "Visual Taxometric Approach to Image Segmentation Using Fuzzy-Spatial Taxon Cut Yields Contextually Relevant Regions." Information Processing and Management of Uncertainty in Knowledge-Based Systems. Springer International Publishing, 2014. ^     ^ 2 May     ^     Further reading [ ]                                                                                 External links [ ] USC Iris computer vision conference list Keith Price's Annotated Computer Vision Bibliography v t e Datasets Digital geometry Commercial systems Feature detection Geometry Image sensor technology Learning Morphology Motion analysis Noise reduction techniques Recognition and categorization Research infrastructure Researchers Segmentation Software Computer stereo vision Autonomous vehicles Face recognition Image search Optical character recognition Remote sensing Robots v t e Virtuality Virtual cinematography Augmented reality Augmented virtuality Real life Projection augmented model Reality–virtuality continuum Artificial reality Simulated reality Ubiquitous computing persistent Multimodal interaction Telepresence Immersion Compositing Camera resectioning Haptic suit optical Head-up display Image-based modeling and rendering Real-time computer graphics Virtual retinal display Wearable computer Chroma key Visual hull Free viewpoint television Omnidirectional treadmill Hidden surface determination Virtual reality headset 360-degree video Omnidirectional camera VR photography stereo Motion capture Tracking system Optical Inertial Magnetic Wired glove Gametrak Google Glass Microsoft HoloLens PlayStation Move Leap Motion Kinect Sixense TrueMotion Daydream Google Cardboard HTC Vive Oculus Rift Samsung Gear VR PlayStation VR OSVR AlloSphere Cave TreadPort Sensorama Virtual Boy Famicom 3D System Sword of Damocles Sega VR Virtuality Pervasive game ARToolKit virtual graffiti Simulated reality in fiction Artificial intelligence Image processing Computer vision Packaging machinery Articles lacking in-text citations from July 2014 All articles lacking in-text citations Articles that may contain original research from July 2014 All articles that may contain original research CS1 maint: Extra text: authors list Articles containing video clips Navigation menu Personal tools Not logged in Talk Contributions Create account Log in Namespaces Article Article Talk Talk Variants
Views Read Read Edit Edit View history View history More More
Navigation Main page Contents Featured content Current events Random article Donate to Wikipedia Wikipedia store Interaction Help About Wikipedia Community portal Recent changes Contact page Tools What links here Related changes Upload file Special pages Permanent link Page information Wikidata item Cite this page Print/export Create a book Download as PDF Printable version In other projects Wikimedia Commons Languages العربية Български Bosanski Català Čeština Eesti Ελληνικά Español Euskara فارسی Français 한국어 Hrvatski Italiano עברית 日本語 Polski Português Русский Shqip Simple English Slovenščina Suomi Svenska ไทย Українська اردو Tiếng Việt 中文 Edit links
This page was last edited on 6 September 2017, at 12:40. Privacy policy About Wikipedia Disclaimers Contact Wikipedia Developers Cookie statement Mobile view