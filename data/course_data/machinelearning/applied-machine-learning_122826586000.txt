Applied Machine Learning – Facebook Research
Applied Machine Learning Connectivity Computer Vision Data Science Economics & Computation Facebook AI Research (FAIR) Human Computer Interaction & UX Natural Language Processing & Speech Security & Privacy Systems & Networking Virtual Reality Publications People The Facebook Fellowship Program Emerging Scholars Faculty Awards Research Collaborations Post-docs and Sabbaticals Research Awards Downloads Careers Blog Applied Machine Learning					 Applying machine learning science to Facebook products Machine learning is essential to Facebook. It helps people discover new content and connect with the stories they care the most about. Our applied machine learning researchers and engineers develop machine learning algorithms that rank feeds, ads and search results, and create new text understanding algorithms that keep spam and misleading content at bay. New computer vision algorithms can “read” images and videos to the blind and display over 2 billion translated stories every day, speech recognition systems automatically caption the videos that play in your news feed, and we create new magical visual experiences such as turning panorama photos into fully interactive 360 photos. <PERSON> <PERSON>, Director of Applied Machine Learning Our People Publications Relationship Proposal Networks <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>
Link the head to the “beak”: Zero Shot Learning from Noisy Text Description at Part Precision Mohamed <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>: Creative Adversarial Networks <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON> Diffusion and Trends in Facebook Photographs <PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> Joo
Blog Downloads Caffe2 is a machine learning framework enabling simple and flexible deep learning. Building on the original Caffe, Caffe2 is designed with expression, speed, and modularity in mind, and allows a more flexible way to organize computation. Want to solve some of the most challenging technology problems? RSS Feed About Careers Privacy Cookies Terms Help © 2017 Facebook