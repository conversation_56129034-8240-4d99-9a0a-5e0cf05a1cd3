Blog - Machine Learning Mastery
Navigation Machine Learning Mastery Making developers awesome at machine learning Home Empty Menu Return to Content Primer on Neural Network Models for Natural Language Processing By <PERSON> on in Natural Language Processing Deep learning is having a large impact on the field of natural language processing. But, as a beginner, where do you start? Both deep learning and natural language processing are huge fields. What are the salient aspects of each field to focus on and which areas of NLP is deep learning having the most impact? […] Continue Reading Oxford Course on Deep Learning for Natural Language Processing By <PERSON> on in Natural Language Processing Deep Learning methods achieve state-of-the-art results on a suite of natural language processing problems What makes this exciting is that single models are trained end-to-end, replacing a suite of specialized statistical models. The University of Oxford in the UK teaches a course on Deep Learning for Natural Language Processing and much of the materials for […] Continue Reading Review of Stanford Course on Deep Learning for Natural Language Processing By <PERSON> on in Natural Language Processing Natural Language Processing, or NLP, is a subfield of machine learning concerned with understanding speech and text data. Statistical methods and statistical machine learning dominate the field and more recently deep learning methods have proven very effective in challenging NLP problems like speech recognition and text translation. In this post, you will discover the Stanford […] Continue Reading Top Books on Natural Language Processing By <PERSON> on in Natural Language Processing Natural Language Processing, or NLP for short, is the study of computational methods for working with speech and text data. The field is dominated by the statistical paradigm and machine learning methods are used for developing predictive models. In this post, you will discover the top books that you can read to get started with […] Continue Reading A Gentle Introduction to RNN Unrolling By Jason Brownlee Jason Brownlee on in Long Short-Term Memory Networks Recurrent neural networks are a type of neural network where the outputs from previous time steps are fed as input to the current time step. This creates a network graph or circuit diagram with cycles, which can make it difficult to understand how information moves through the network. In this post, you will discover the […] Continue Reading Making Predictions with Sequences By Jason Brownlee Jason Brownlee on in Long Short-Term Memory Networks Sequence prediction is different from other types of supervised learning problems. The sequence imposes an order on the observations that must be preserved when training models and making predictions. Generally, prediction problems that involve sequence data are referred to as sequence prediction problems, although there are a suite of problems that differ based on the […] Continue Reading How to Diagnose Overfitting and Underfitting of LSTM Models By Jason Brownlee Jason Brownlee on in Long Short-Term Memory Networks It can be difficult to determine whether your Long Short-Term Memory model is performing well on your sequence prediction problem. You may be getting a good model skill score, but it is important to know whether your model is a good fit for your data or if it is underfit or overfit and could do […] Continue Reading How to Reshape Input Data for Long Short-Term Memory Networks in Keras By Jason Brownlee Jason Brownlee on in Long Short-Term Memory Networks It can be difficult to understand how to prepare your sequence data for input to an LSTM model. Often there is confusion around how to define the input layer for the LSTM model. There is also confusion about how to convert your sequence data that may be a 1D or 2D matrix of numbers to […] Continue Reading How to Make Predictions with Long Short-Term Memory Models in Keras By Jason Brownlee Jason Brownlee on in Long Short-Term Memory Networks The goal of developing an LSTM model is a final model that you can use on your sequence prediction problem. In this post, you will discover how to finalize your model and use it to make predictions on new data. After completing this post, you will know: How to train a final LSTM model. How […] Continue Reading Gentle Introduction to Generative Long Short-Term Memory Networks By Jason Brownlee Jason Brownlee on in Long Short-Term Memory Networks The Long Short-Term Memory recurrent neural network was developed for sequence prediction. In addition to sequence prediction problems. LSTMs can also be used as a generative model In this post, you will discover how LSTMs can be used as generative models. After completing this post, you will know: About generative models, with a focus on […] Continue Reading 1 … Welcome to Machine Learning Mastery Read More You’re a Professional (and you need results)! Take Action Now! Get The Training You Need Popular July 21, 2016 June 10, 2016 May 24, 2016 March 13, 2017 July 26, 2016 June 2, 2016 April 7, 2017 June 9, 2016 November 7, 2016 November 25, 2013 © 2017 Machine Learning Mastery. All Rights Reserved.