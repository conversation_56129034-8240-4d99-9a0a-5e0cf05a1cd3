A Beginner’s Guide to AI/ML 🤖👶 – Machine Learning for Humans – Medium
Homepage Machine Learning for Humans Follow Homepage Blocked Unblock Follow Following Machine Learning for Humans🤖👶 Simple, plain-English explanations accompanied by math, code, and real-world examples. Roadmap Who should read this? Technical people who want to get up to speed on machine learning quickly Non-technical people who want a primer on machine learning and are willing to engage with technical concepts Anyone who is curious about how machines think This guide is intended to be accessible to anyone. Basic concepts in probability, statistics, programming, linear algebra, and calculus will be discussed, but it isn’t necessary to have prior knowledge of them to gain value from this series. Why machine learning matters Artificial intelligence will shape our future more powerfully than any other innovation this century. Anyone who does not understand it will soon find themselves feeling left behind, waking up in a world full of technology that feels more and more like magic. In 2015, Google trained a conversational agent (AI) that could not only convincingly interact with humans as a tech support helpdesk, but also discuss morality, express opinions, and answer general facts-based questions. Just a few days ago (as of this writing), on August 11, 2017, OpenAI reached yet another incredible milestone by defeating the world’s top professionals in 1v1 matches of the online multiplayer game Dota 2. Much of our day-to-day technology is powered by artificial intelligence. Point your camera at the menu during your next trip to Taiwan and the restaurant’s selections will magically appear in English via the Google Translate app. In everyday life, it’s increasingly commonplace to discover machines in roles traditionally occupied by humans. Really, don’t be surprised if a little housekeeping delivery bot shows up instead of a human next time you call the hotel desk to send up some toothpaste. In this series, we’ll explore the core machine learning concepts behind these technologies. By the end, you should be able to describe how they work at a conceptual level and be equipped with the tools to start building similar applications yourself. The semantic tree: artificial intelligence and machine learning Strong AI will change our world forever; to understand how, studying machine learning is a good place to start Machine learning is at the core of our journey towards artificial general intelligence, and in the meantime, it will change every industry and have a massive impact on our day-to-day lives. That’s why we believe it’s worth understanding machine learning, at least at a conceptual level — and we designed this series to be the best place to start. How to read this series You don’t necessarily need to read the series cover-to-cover to get value out of it. Here are three suggestions on how to approach it, depending on your interests and how much time you have: About the authors Most of this series was written during a 10-day trip to the United Kingdom in a frantic blur of trains, planes, cafes, pubs and wherever else we could find a dry place to sit. Our aim was to solidify our own understanding of artificial intelligence, machine learning, and how the methods therein fit together — and hopefully create something worth sharing in the process. Part 2.1: Supervised Learning Part 2.2: Supervised Learning II Part 2.3: Supervised Learning III Part 3: Unsupervised Learning Part 4: Neural Networks & Deep Learning Part 5: Reinforcement Learning Appendix: The Best Machine Learning Resources Machine Learning Artificial Intelligence Deep Learning Reinforcement Learning Tech Blocked Unblock Follow Following Vishal Maini Follow Machine Learning for Humans Demystifying artificial intelligence & machine learning. Discussions on safe and intentional application of AI for positive social impact. Share Get updates Get updates