A Machine Learning Introductory Tutorial with Examples | Toptal
Start hiring Login Top 3% Why Clients Partners Community Blog About Us Start hiring Apply as a Developer Login Questions? Contact Us Start hiring Login An Introduction to Machine Learning Theory and Its Applications: A Visual Tutorial with Examples 0 shares <PERSON>ela Ordaz Machine Learning (ML) is coming into its own, with a growing recognition that ML can play a key role in a wide range of critical applications, such as data mining, natural language processing, image recognition, and expert systems.
ML provides potential solutions in all these domains and more, and is set to be a pillar of our future civilization. What is Machine Learning? So if you want your program to predict, for example, traffic patterns at a busy intersection (task T), you can run it through a machine learning algorithm with data about past traffic patterns (experience E) and, if it has successfully “learned”, it will then do better at predicting future traffic patterns (performance measure P). Among the different types of ML tasks, a crucial distinction is drawn between supervised and unsupervised learning: We will primarily focus on supervised learning here, but the end of the article includes a brief discussion of unsupervised learning with some links for those who are interested in pursuing the topic further. Supervised Machine Learning So let’s say our simple predictor has this form: A Simple Machine Learning Example We stick to simple problems in this post for the sake of illustration, but the reason ML exists is because, in the real world, the problems are much more complex. On this flat screen we can draw you a picture of, at most, a three-dimensional data set, but ML problems commonly deal with data with millions of dimensions, and very complex predictor functions.
ML solves problems that cannot be solved by numerical means alone. With that in mind, let’s look at a simple example. Say we have the following training data, wherein company employees have rated their satisfaction on a scale of 1 to 100: First, notice that the data is a little noisy. That is, while we can see that there is a pattern to it (i.e. employee satisfaction tends to go up as salary goes up), it does not all fit neatly on a straight line. This will always be the case with real-world data (and we absolutely want to train our machine using real-world data!). So then how can we train a machine to perfectly predict an employee’s level of satisfaction? The answer, of course, is that we can’t.
The goal of ML is never to make “perfect” guesses, because ML deals in domains where there is no such thing. The goal is to make guesses that are good enough to be useful. If we ask this predictor for the satisfaction of an employee making $60k, it would predict a rating of 27: It’s obvious that this was a terrible guess and that this machine doesn’t know very much. And if we repeat this process, say 1500 times, our predictor will end up looking like this: Now we’re getting somewhere. A Note on Complexity This function takes input in four dimensions and has a variety of polynomial terms. Deriving a normal equation for this function is a significant challenge. Many modern machine learning problems take thousands or even millions of dimensions of data to build predictions using hundreds of coefficients. Predicting how an organism’s genome will be expressed, or what the climate will be like in fifty years, are examples of such complex problems. Fortunately, the iterative approach taken by ML systems is much more resilient in the face of such complexity. Instead of using brute force, a machine learning system “feels its way” to the answer. For big problems, this works much better. While this doesn’t mean that ML can solve all arbitrarily complex problems (it can’t), it does make for an incredibly flexible and powerful tool. Gradient Descent - Minimizing “Wrongness” With least squares, the penalty for a bad guess goes up quadratically with the difference between the guess and the correct answer, so it acts as a very “strict” measurement of wrongness. The cost function computes an average penalty over all of the training examples. Consider the following plot of a cost function for some particular ML problem: That covers the basic theory underlying the majority of supervised Machine Learning systems. But the basic concepts can be applied in a variety of different ways, depending on the problem at hand. Classification Problems Under supervised ML, two major subcategories are: Our examples so far have focused on regression problems, so let’s now also take a look at a classification example. In classification, a regression predictor is not very useful. What we usually want is a predictor that makes a guess somewhere between 0 and 1. In a cookie quality classifier, a prediction of 1 would represent a very confident guess that the cookie is perfect and utterly mouthwatering. A prediction of 0 represents high confidence that the cookie is an embarrassment to the cookie industry. Values falling within this range represent less confidence, so we might design our system such that prediction of 0.6 means “Man, that’s a tough call, but I’m gonna go with yes, you can sell that cookie,” while a value exactly in the middle, at 0.5, might represent complete uncertainty. This isn’t always how confidence is distributed in a classifier but it’s a very common design and works for purposes of our illustration. so that our predictor becomes: Notice that the sigmoid function transforms our output into the range between 0 and 1. This behavior is captured by the log function, such that: A classification predictor can be visualized by drawing the boundary line; i.e., the barrier where the prediction changes from a “yes” (a prediction greater than 0.5) to a “no” (a prediction less than 0.5). With a well-designed system, our cookie data can generate a classification boundary that looks like this: Now that’s a machine that knows a thing or two about cookies! An Introduction to Neural Networks Neural networks are well suited to machine learning problems where the number of inputs is gigantic. The computational cost of handling such a problem is just too overwhelming for the types of systems we’ve discussed above. As it turns out, however, neural networks can be effectively tuned using techniques that are strikingly similar to gradient descent in principle. Unsupervised Machine Learning Unsupervised learning typically is tasked with finding relationships within data. There are no training examples used in this process. Instead, the system is given a set data and tasked with finding patterns and correlations therein.
A good example is identifying close-knit groups of friends in social network data. Conclusion We’ve covered much of the basic theory underlying the field of Machine Learning here, but of course, we have only barely scratched the surface. Keep in mind that to really apply the theories contained in this introduction to real life machine learning examples, a much deeper understanding of the topics discussed herein is necessary. There are many subtleties and pitfalls in ML, and many ways to be lead astray by what appears to be a perfectly well-tuned thinking machine. Almost every part of the basic theory can be played with and altered endlessly, and the results are often fascinating. Many grow into whole new fields of study that are better suited to particular problems. Acknowledgement About the author    0 shares Comments Disqus 0 shares Working with Angular 4 Forms: Input Validation 2 days ago 2 days ago How to Choose the Best Front-end Framework 3 days ago 3 days ago Implementing Serverless Node.js Functions Using Google Cloud 6 days ago 6 days ago Common Mistakes in Client Communication: How to Not Frustrate Your Client 9 days ago 9 days ago Conquer String Search with the Aho-Corasick Algorithm 12 days ago 12 days ago Web Accessibility: Why W3C Standards Are Often Ignored 17 days ago 17 days ago Maximum Flow and the Linear Assignment Problem 20 days ago 20 days ago Getting Started with the SRVB Cryptosystem 23 days ago 23 days ago Back-End Machine Learning Big Data Toptal Developers Android Developers AngularJS Developers Back-End Developers C++ Developers Data Scientists DevOps Engineers Ember.js Developers Freelance Developers Front-End Developers Full Stack Developers HTML5 Developers iOS Developers Java Developers JavaScript Developers Machine Learning Engineers Magento Developers Mobile App Developers .NET Developers Node.js Developers PHP Developers Python Developers React.js Developers Ruby Developers Ruby on Rails Developers Salesforce Developers Scala Developers Software Developers Unity or Unity3D Developers Web Developers WordPress Developers Join the Toptal community. Highest In-Demand Talent iOS Developer Front-End Developer UX Designer UI Designer Financial Modeling Consultants Interim CFOs About Top 3% Clients Freelance Developers Freelance Designers Freelance Finance Experts About Us Contact Contact Us Press Center Careers FAQ Social Facebook Twitter Google+ LinkedIn Hire the top 3% of freelance talent © Copyright 2010 - 2017 Toptal, LLC © Copyright 2010 - 2017 Toptal, LLC Privacy Policy Website Terms Home Home Blog Blog An Introduction to Machine Learning Theory and Its Applications: A Visual Tutorial with Examples An Introduction to Machine Learning Theory and Its Applications: A Visual Tutorial with Examples