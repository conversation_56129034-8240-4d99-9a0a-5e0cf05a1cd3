Amazon Machine Learning - Predictive Analytics with AWS
Click here to return to Amazon Web Services homepage More My Account English
Explore AWS solutions and products Deutsch English Español Français Italiano Português Ρусский 日本語 한국어 中文 (简体) 中文 (繁體) MY ACCOUNT AWS Management Console Account Settings Billing & Cost Management Security Credentials AWS Personal Health Dashboard Start-ups use AWS for everything their app needs.
Enterprises use AWS to deliver IT innovation globally while reducing costs.
Public Sector organizations use AWS to improve agility while reducing costs. Start developing on Amazon Web Services using one of our pre-built sample apps.
SDKs, IDE Toolkits, Command Line Tools, and Developer Tools for AWS. Tooling and infrastructure resources for DevOps. Build and run applications without thinking about servers
Learn more about the AWS Partner Network and supporting Partner Programs Find qualified APN Partners to help you with your AWS projects
Learn more about top APN Consulting Partners globally Find validated partner solutions that run on or integrate with AWS, by key vertical and solution areas. Download content, access training, and engage with AWS through the partner-only AWS site Power web, social, and mobile apps in the cloud Tooling and infrastructure resources for DevOps Build and run applications without thinking about servers Build secure and scalable online storefronts Highly scalable online advertising and marketing services in the cloud Store and retrieve any data, anywhere, any time Archive your data for long-term retention Recover your systems and data quickly from a disaster Store and process large datasets to solve business problems Run tightly-coupled and IO-intensive workloads to solve complex science, engineering and business problems Quickly build connected devices with backend services
Build and run applications without thinking about servers Customer applications, data analytics, storage, compliance, and security in the cloud. Services and infrastructure for mobile, web, PC, and console games Cross-platform building blocks to help you make games Media storage, archiving, processing, and delivery from the cloud Deliver improved care to patients with reduced time and effort Services and infrastructure to help your company solve complex problems
Access powerful computing tools to run genomics workloads Run business-critical applications in a secure and robust environment Run your Oracle applications on the AWS Cloud Run your SAP workloads and applications on AWS’s scalable infrastructure All of your Microsoft applications in the AWS Cloud Virtual Servers in the Cloud Store and Retrieve Docker Images Run and Manage Docker Containers Launch and Manage Virtual Private Servers Isolated Cloud Resources Run Batch Jobs at Any Scale Run and Manage Web Apps Run Code without Thinking about Servers Scalable Storage in the Cloud EC2 Block Storage Volumes Fully Managed File System for EC2 Low-cost Archive Storage in the Cloud Hybrid Storage Integration Petabyte-scale Data Transport Petabyte-scale Data Transport with On-board Compute Exabyte-scale Data Transport High Performance Managed Relational Database Managed Relational Database Service for MySQL, PostgreSQL, Oracle, SQL Server, and MariaDB Managed NoSQL Database Fully Managed, In-memory Cache for DynamoDB In-memory Caching Service Fast, Simple, Cost-Effective Data Warehousing Migrate Databases with Minimal Downtime Track Migrations from a Single Place Discover on-premises applications to streamline migration Migrate Databases with Minimal Downtime Convert Database Schema and Migrate Warehouses Migrate On-premises servers to AWS Petabyte-scale Data Transport Petabyte-scale Data Transport with On-board Compute Exabyte-scale Data Transport Isolated Cloud Resources
Global Content Delivery Network Scalable Domain Name System (DNS) Dedicated Network Connection to AWS
Develop and Deploy AWS Applications Store Code in Private Git Repositories Build and Test Code Automate Code Deployments Release Software using Continuous Delivery Analyze and Debug Your Applications Unified Tool to Manage AWS Services Tools and SDKs for AWS Analyze Your AWS Cost and Usage Set Custom Cost and Usage Budgets Dive Deeper into Your Reserved Instances (RIs) Access Comprehensive Cost and Usage Information Monitor Resources and Applications Configure and Manage EC2 Instances and On-premises Servers Create and Manage Resources with Templates Track User Activity and API Usage Track Resource Inventory and Changes Automate Operations with Chef Create and Use Standardized Products Optimize Performance and Security Personalized view of AWS service health Unified Tool to Manage AWS Services Web-based User Interface Infrastructure Operations Management for AWS Manage User Access and Encryption Keys Create Flexible Cloud-Native Directories Analyze Application Security Discover, Classify, and Protect Your Data Provision, Manage, and Deploy SSL/TLS Certificates Hardware-based Key Storage for Regulatory Compliance Host and Manage Active Directory Managed Creation and Control of Encryption Keys Policy-based management for multiple AWS accounts DDoS Protection Filter Malicious Web Traffic Build Voice and Text Chatbots Turn Text into Lifelike Speech Search and Analyze Images Machine Learning for Developers Scalable, Open-source Deep Learning Framework A Common Deep Learning Framework Deep Learning on Amazon EC2 Query Data in S3 using SQL Hosted Hadoop Framework
Managed Search Service Run and Scale Elasticsearch Clusters Work with Real-time Streaming Data Fast, Simple, Cost-effective Data Warehousing Fast Business Analytics Service Orchestration Service for Periodic, Data-Driven Workflows
Prepare and Load Data Build, Test, and Monitor Mobile Apps Build, Deploy, and Manage APIs
User Identity and App Data Synchronization Push Notifications for Mobile Apps Test Android, iOS, and Web Apps on Real Devices in the AWS Cloud Build High Quality Mobile Apps Quickly and Easily Build, Test, and Monitor Mobile Apps Build, Deploy, and Manage APIs User Identity and App Data Synchronization Push Notifications for Mobile Apps Test Android, iOS, and Web Apps on Real Devices in the AWS Cloud Build High Quality Mobile Apps Quickly and Easily
Coordinate Distributed Applications Build, Publish and Manage APIs
Easy-to-use Scalable Media Transcoding Managed Message Queues Pub/Sub, Mobile Push and SMS
Push Notifications for Mobile Apps Email Sending and Receiving Secure Enterprise Document Storage and Sharing
Secure Email and Calendaring Frustration-free meetings, video calls, and chat
Virtual Desktops in the Cloud
Stream desktop applications securely to a browser Connect Devices to the Cloud Local Compute, Messaging, and Sync for Devices Cloud Programmable Dash Button Cloud-based contact center service Amazon GameLift: Simple, fast, cost-effective dedicated game server hosting. A Free Cross-Platform 3D Game Engine, with Full Source, Integrated with AWS and Twitch Find calculators and other tools to help you lower costs with the AWS Cloud.
Learn about AWS Cloud security and how to build secure applications.
Learn about the compliance programs on the AWS Cloud and establishing controls Learn how to build scalable and reliable applications in the AWS Cloud.
Get answers to frequently asked technical support questions. Get clear guidance from AWS architects and engineers on common user questions.
Amazon EC2 Amazon EC2 Container Registry Amazon EC2 Container Service Amazon Lightsail Amazon VPC AWS Batch AWS Elastic Beanstalk AWS Lambda Auto Scaling Elastic Load Balancing Amazon Simple Storage Service (S3) Amazon Elastic Block Storage (EBS) Amazon Elastic File System (EFS) Amazon Glacier AWS Storage Gateway AWS Snowball AWS Snowball Edge AWS Snowmobile Amazon Aurora Amazon RDS Amazon DynamoDB Amazon DynamoDB Accelerator (DAX) Amazon ElastiCache Amazon Redshift AWS Database Migration Service AWS Migration Hub AWS Application Discovery Service AWS Database Migration Service AWS Schema Conversion Tool AWS Server Migration Service AWS Snowball AWS Snowball Edge AWS Snowmobile Amazon VPC Amazon CloudFront Amazon Route 53 AWS Direct Connect Elastic Load Balancing AWS CodeStar AWS CodeCommit AWS CodeBuild AWS CodeDeploy AWS CodePipeline AWS X-Ray AWS Tools & SDKs Amazon CloudWatch Amazon EC2 Systems Manager AWS CloudFormation AWS CloudTrail AWS Config AWS OpsWorks AWS Service Catalog AWS Trusted Advisor AWS Personal Health Dashboard AWS Command Line Interface AWS Management Console AWS Managed Services Amazon Lex Amazon Polly Amazon Rekognition Amazon Machine Learning Apache MXNet on AWS TensorFlow on AWS AWS Deep Learning AMIs Amazon Athena Amazon EMR Amazon CloudSearch Amazon Elasticsearch Service Amazon Kinesis Amazon Redshift Amazon QuickSight AWS Data Pipeline AWS Glue AWS Identity and Access Management (IAM) Amazon Cloud Directory Amazon Inspector Amazon Macie AWS Certificate Manager AWS CloudHSM AWS Directory Service AWS Key Management Service AWS Organizations AWS Shield AWS WAF AWS Artifact AWS Mobile Hub Amazon API Gateway Amazon Cognito Amazon Pinpoint AWS Device Farm AWS Mobile SDK AWS Cost Explorer AWS Budgets Reserved Instance Reporting AWS Cost and Usage Report AWS Step Functions Amazon API Gateway Amazon Elastic Transcoder Amazon Simple Queue Service (SQS) Amazon Simple Notification Service (SNS) Amazon Pinpoint Amazon Simple Email Service (SES) Amazon Chime Amazon WorkDocs Amazon WorkMail Amazon WorkSpaces Amazon AppStream 2.0 AWS Marketplace AWS IoT Platform AWS Greengrass AWS IoT Button Amazon Connect Amazon GameLift Amazon Lumberyard Click here to return to Amazon Web Services homepage Products & Services Related Links Get Started with AWS for Free Amazon Machine Learning is a service that makes it easy for developers of all skill levels to use machine learning technology. Amazon Machine Learning provides visualization tools and wizards that guide you through the process of creating machine learning (ML) models without having to learn complex ML algorithms and technology. Once your models are ready, Amazon Machine Learning makes it easy to obtain predictions for your application using simple APIs, without having to implement custom prediction generation code, or manage any infrastructure. Amazon Machine Learning is based on the same proven, highly scalable, ML technology used for years by Amazon’s internal data scientist community. The service uses powerful algorithms to create ML models by finding patterns in your existing data. Then, Amazon Machine Learning uses these models to process new data and generate predictions for your application. Amazon Machine Learning is highly scalable and can generate billions of predictions daily, and serve those predictions in real-time and at high throughput. With Amazon Machine Learning, there is no upfront hardware or software investment, and you pay as you go, so you can start small and scale as your application grows. Introducing Amazon Machine Learning Readmission Prediction Through Patient Risk Stratification Using Amazon Machine Learning Building a Binary Classification Model with Amazon Machine Learning and Amazon Redshift.
Chat with experts and learn about AWS AI by registering for the upcoming tech talk: Sep 06 Apache MXNet Version 0.11 Now Supports Apple Core ML and Keras
Apr 18 Deep Learning AMI for Ubuntu v1.3_Apr2017 now Supports Caffe2 Mar 17 Deep Learning AMI release v1.2 for Ubuntu and Updated AWS CloudFormation Template Now Available
Mar 08 Deep Learning AMI release v2.0 now Available for Amazon Linux
Feb 10 Ubuntu version of AWS Deep Learning AMI Now Available
Amazon Machine Learning partners help customers build Amazon Machine Learning-powered smarter systems. Amazon ML is a service that makes it easy for developers of all skill levels to use machine learning technology.  47Lining is an AWS Advanced Consulting Partner with Big Data Competency designation. 47Lining develops big data solutions and delivers big data managed services built from underlying AWS big data building blocks like Amazon Redshift, Kinesis, S3, DynamoDB, Machine Learning and Elastic MapReduce. 47Lining helps customers build, operate and manage breathtaking “Data Machines” for their data-driven businesses.
Amazon Machine Learning is a managed service that provides end-to-end model creation, deployment, and monitoring. Once your model is ready, you can quickly and reliably generate predictions for your applications, eliminating the time and investment needed to build, scale, and maintain machine learning infrastructure. Amazon Machine Learning prediction APIs can be used to generate billions of predictions for your applications. You can request predictions for large numbers of data records all at once using the batch prediction API, or use the real-time API to obtain predictions for individual data records, and use them within interactive web, mobile, or desktop applications.
With Amazon Machine Learning there is no setup cost and you pay as you go, so you can start small and scale as your application grows.
Amazon Machine Learning is based on the same proven, highly scalable, ML technology used by Amazon to perform critical functions like supply chain management, fraudulent transaction identification, and catalog organization. Amazon Machine Learning makes it easy to build predictive models that help identify potentially fraudulent retail transactions, or detect fraudulent or inappropriate item reviews.
Amazon Machine Learning can help you deliver targeted marketing campaigns. For example, Amazon Machine Learning could use prior customer activity to choose the most relevant email campaigns for target customers.
Amazon Machine Learning can help you process unstructured text and take actions based on content. For instance, Amazon Machine Learning could be used to build applications that classify product reviews as positive, negative, or neutral.
Amazon Machine Learning can help you find customers who are at high risk of attrition, enabling you to proactively engage them with promotions or customer service outreach.
Amazon Machine Learning can process free-form feedback from your customers, including email messages, comments or phone conversation transcripts, and recommend actions that can best address their concerns. For example, you can use Amazon Machine Learning to analyze social media traffic to discover customers who have a product support issue, and connect them with the right customer care specialists. What is Cloud Computing? What is Caching? What is NoSQL? What is DevOps? Products & Services Customer Success Economics Center Architecture Center Security Center What's New Whitepapers AWS Blog Events Sustainable Energy Press Releases AWS in the News Analyst Reports Legal Websites & Website Hosting Business Applications Backup & Recovery Disaster Recovery Data Archive DevOps Serverless Computing Big Data High Performance Computing Mobile Services Digital Marketing Game Development Digital Media Government & Education Health Financial Services Windows on AWS Developers Java on AWS JavaScript on AWS Mobile on AWS PHP on AWS Python on AWS Ruby on AWS Windows & .NET on AWS SDKs & Tools AWS Marketplace User Groups Support Plans Service Health Dashboard Discussion Forums FAQs Documentation Articles & Tutorials Test Drives AWS Business Builder Management Console Billing & Cost Management Subscribe to Updates Personal Information Payment Method AWS Identity & Access Management Security Credentials Request Service Limit Increases Contact Us Amazon Web Services is Hiring. Amazon Web Services is an Equal Opportunity Employer. An amazon.com company Language Deutsch English Español Français Italiano Português Ρусский 日本語 한국어 中文 (简体) 中文 (繁體) Site Terms | Privacy