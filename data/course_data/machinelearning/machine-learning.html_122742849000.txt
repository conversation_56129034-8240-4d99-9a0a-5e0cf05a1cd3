Machine Learning | SAP
Search Products Products Industries Industries Support Support Training Training Community Community Developer Developer Partner Partner About About Search Products Search ERP and Digital Core Search Cloud and Data Platforms Search Procurement and Networks Search Analytics Search Customer Engagement and Commerce Search IoT and Digital Supply Chain Search Human Resources (HR) Search Finance Search Spotlights Search Industries Search Energy and Natural Resources Search Financial Services Search Consumer Industries Search Discrete Industries Search Service Industries Search Public Services Search Spotlights Search Support Search SAP Support Plans Search SAP Digital Business Services Search SAP Support Portal Search SAP Help Portal Search Spotlights Search Training Search Free SAP Training Search SAP Learning Hub Online Training and Enablement Search SAP Education and Course Directory Search Certification at SAP Search Spotlights Search Community Search Browse Community Search Blogs Search Questions and Answers Search SAP Community Search Community Support Search My User Profile Search Spotlights Search Developer Search Developer Products Search Developer Topics Search Tutorial Navigator Search Developer Events Search Developer Resources Search Developer Showcases Search Trials / Downloads Search SAP Cloud Platform Blueprints Search Spotlights Search Partner Search Find a Partner Search Become a Partner Search Already a Partner Search Certify My Solution Search Refer Leads to SAP Search About Search Global Company Information Search Investor Relations Search Careers Search News and Press Search Customer Involvement Search Events Search Customer Testimonials Search About SAP North America Search Spotlights Search Log On About SAP SE: English Über SAP SE: Deutsch Argentina Bolivia Brasil Canada - English Canada - Français Chile Colombia Costa Rica Ecuador El Salvador Guatemala Honduras Latin America México Panamá Perú Puerto Rico República Dominicana United States Venezuela Albanija Belgique België Bosna i Hercegovina Crna Gora Danmark Deutschland Eesti España France Hrvatska Ireland Italia Latvija Lietuva Magyarország Makedonija Nederland Norge Österreich Polska Portugal Romania Schweiz Slovenija Slovensko Srbija Suisse Suomi Sverige Türkiye United Kingdom Česká republika Ελλάδα και Κύπρος Азербайджан Армения Беларусь България Грузия Молдавия Россия СНГ Украина Africa Angola Bahrain Egypt Iraq Jordan Kenya Kuwait Lebanon Libya MENA MENA (‏العربية‎) Morocco Nigeria Oman Pakistan Palestine Qatar Saudi Arabia South Africa United Arab Emirates Yemen ישראל الأردن‎ الامارات العربية المتحدة البحرين الجمهورية اليمنية العـراق الكويت اسلامی جمہوریۂ پاكستان المملكة العربية السعودية عُمان فلسطين‎ قطر لبنان‎ ليبيا مصر Australia Hongkong India Indonesia Malaysia New Zealand Philippines Singapore South East Asia Thailand Việt Nam Казахстан Киргизия Таджикистан Туркменистан Узбекистан 中国 台灣 日本 대한민국 United States Excellent Very Good Good Fair Poor Very easy Easy Neutral Difficult Very difficult Send your Feedback Close Call us at Or see our complete list of
local country numbers
Chat Now
Chat Offline
Get live help and chat with an SAP representative. Contact Us E-mail us with comments, questions or feedback. Home Trends Machine Learning Intro Benefits FAQs Get Started News
Machine Learning
Machine learning and the larger world of artificial intelligence (AI) are no longer the stuff of science fiction. They’re here – and many businesses are already taking advantage. As a new breed of software that is able to learn without being explicitly programmed, machine learning (and deep learning) can access, analyze, and find patterns in Big Data in a way that is beyond human capabilities. And now we’ve made it easier to unlock its potential with embedded machine learning capabilities and services easily accessible through the cloud. Artificial intelligence, machine learning, and deep learning are often used interchangeably, but they’re not the same. In a nutshell, AI is the broader concept of machines that can act intelligently. Machine learning and deep learning are sub-sets of AI based on the idea that given access to large volumes of data, machines can learn for themselves. Read on for more about deep learning vs. machine learning, and other important terms. What is deep learning? Facial recognition software Self-driving cars Smart home automation devices Supervised vs unsupervised learning Supervised learning – in this approach humans label the inputs and outputs and then the model figures out the rules for connecting the two. Unsupervised learning – algorithms are left to discover patterns in the data (which is sometimes clustered) on their own.
Why is machine learning advancing so rapidly?
AI and machine learning in action
Previous
Welcome to the future of business
Becoming an intelligent enterprise
Shoe design goes digital
Thanks to a co-innovation project in Chengdu, China, Aimickey Shoe Company is using SAP machine learning technology to help customers design and virtually “try on” their own shoes – gaining a distinct competitive advantage in the process. Next Benefits of machine learning Previous Machine learning can automate and prioritize routine decision making processes – so you can achieve best outcomes sooner. For example, when coupled with the Internet of Things, it can help you decide what to fix first in your manufacturing plant. Innovation and growth Next Previous With machine-aided business processes and faster overall workflows, you can optimize business operations and your product and service offerings – so you can do and sell more while lowering back-office costs and TCO.
Better outcomes
Next Machine learning use cases Previous
Smart business processes
Digital assistants and bots
Next How do you ensure accurate results? Start with clean data sets and ensure input data is labeled and categorized correctly to minimize false positives Consider potential biases inherent in your data – if it’s garbage in, it’ll be garbage coming out. Ask questions and create processes for evaluating algorithms to avoid this Use the right algorithm training method for your goal (e.g., supervised for predicting the sales price of a home on known variables) Complete thorough machine learning training to boost learning outcomes Can you trust the decisions that machines make? The idea of machines taking over our lives and livelihoods has made for some great movies, but the reality is far less dramatic. That’s not to say that we should put blind faith in outcomes uncovered through the machine learning process. Here’s how you can keep AI on track to produce reliable results:   Conduct a proof of concept so you feel confident in the decisions that are being made Supervise processes and results and make adjustments as needed Adjust confidence levels by applying business rules in algorithms
Include feedback mechanisms in your machine learning training process How can we prepare our data? Access to large data sets and machine learning go hand in hand – so minimizing information silos is a critical first step:   Integrate your enterprise data – from suppliers, partners, customers, and more – to give algorithms open access to all relevant data Engage your Chief Data Officer in the machine learning process Consider using a cloud platform that can process high volumes of data integrated from different data sources
How will machine learning fit into the workplace? Result in higher paid jobs that emphasize creativity, problem solving, and knowledge work Automate boring, repetitive tasks to make jobs more interesting (and fun!) Will I require specialized skills to use machine learning? In the past, you needed specialized talent to put machine learning into action: “Quants” who are educated in the language and methods and “translators” who could bridge the disciplines of data, machine learning, and decision making to reframe complex results into executable insights. What about ROI?
Learn more about SAP Leonardo Previous Extend your reach Our machine learning technology taps the largest data pool in the world – leveraging SAP systems across 25 industries and 12 lines of business. With our market leadership, you can get insights not available anywhere else. Integrate quickly Integrate intelligent solutions into your systems quickly and simply – and accelerate ROI – with “out of the box” machine learning capabilities embedded directly into the SAP Cloud Platform and natively built into all of our software applications. Be first to market with AI-driven product innovations and business models that delight customers and drive revenue. And use technology that self-optimizes and re-learns to continuously improve business outcomes and shrink cost and risk. Next Machine learning applications, platform, and services AI platform: Building your intelligent foundation Create, run, consume, and maintain self-learning apps with ease – no data science skills required. SAP Leonardo Machine Learning Foundation connects developers, partners, and customers to machine learning technology through SAP Cloud Platform.     Get quick access to intelligent automation applications Access versatile horizontal and vertical business services and data Use service APIs, embedded AI, and a global marketplace to quickly build smart apps Develop on an open, scalable platform with an intuitive, modern user experience (UX) Finance: Automating payment matching Improve days of sales outstanding Integrate with SAP S/4HANA to reduce TCO and time to value Fraud detection: Improving the accuracy of alerts Zero in on potential fraud cases and boost the accuracy of your alerts with SAP Business Integrity Screening, software that uses predictive algorithms to analyze your historical data.   Focus on the cases with the highest likelihood of fraud and ROI Integrate with SAP HANA to reduce TCO and time to value Rely on models that update as fraud patterns evolve Use a mix of custom and third-party algorithms optimized for your business Recruiting: Finding the best talent with intelligent job matching Put the days of sifting through thousands of resumes behind you – with our intelligent job matching application. SAP Resume Matching uses machine learning to automate the screening process and zero in on the best candidates or jobs without bias.
Devote more time to corporate brand leadership Marketing: Logo and brand recognition Better evaluate your advertising and sponsorship campaigns with SAP Brand Impact. Using advanced computer vision techniques, the application can automatically recognize logos in images and videos – giving your agency or production company accurate, timely insights into marketing ROI. Leverage fast, near-real time brand analysis through an interactive interface that lets you audit all outputs Rely on accurate analyses scalable to millions of hours of footage Review outcomes second-by-second, compare and filter out brand assets, and view aggregated statistics Combine data with your CRM and ERP software and website stats via a time-annotated impact indicator API Customer service: Gathering, analyzing, and responding to feedback Accelerate customer service in your omni-channel front office. SAP Service Ticket Intelligence lets you efficiently process inbound social media posts, e-mails, and other channel interactions by automatically determining classifications, routing, and responses. Improve service response times with automated processing Integrate with SAP Hybris Service Cloud for faster time to value Process more digital interactions without sacrificing quality Sales & marketing: Loyalty and retention Anticipate customers’ behavior, such as product cancellations or renewals, with instant insights from transactional data and digital interaction points. SAP Customer Retention uses advanced machine learning to mine, predict, and capture leading churn indicators – all automatically. Based on the results and your company priorities, you can define and execute next best actions more efficiently. Spot and classify interaction patterns Detect dissatisfied customers, understand root causes, and act on timely predictions Build customer loyalty with proactive retention strategies Previous Machine learning is ideal for scenarios with complex rules and unknown elements, for making predictions on new rather than historical data, and for automating highly repetitive tasks. In other cases, rules-based programming is fine.
Next News and fresh perspectives on machine learning Previous Next Privacy Terms of Use Legal Disclosure Copyright Trademark Sitemap Newsletter Text View Privacy Terms of Use Legal Disclosure Copyright Trademark Sitemap Newsletter Text View