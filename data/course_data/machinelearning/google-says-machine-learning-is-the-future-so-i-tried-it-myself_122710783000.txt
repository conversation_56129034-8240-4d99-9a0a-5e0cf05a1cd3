Google says machine learning is the future. So I tried it myself | Technology | The Guardian
Close The Guardian - Back to home contribution
subscribe
find a job jobs All sections What term do you want to search? Search with google US edition US
edition:
switch to the
UK edition UK switch to the
US switch to the
Australia edition AU switch to the
International edition INT sign in
become a supporter
subscribe search switch to the
UK edition switch to the
Australia edition switch to the
International edition The Guardian - Back to home › › home selected browse
sections close
Google says machine learning is the future. So I tried it myself
If deep learning will be as big as the internet, it’s time for everyone to start looking closely at it
Google says machine learning is the future. So I tried it myself
If deep learning will be as big as the internet, it’s time for everyone to start looking closely at it Share on Facebook Share on Twitter Share via Email View more sharing options Share on LinkedIn Share on Pinterest Share on Google+ Share on WhatsApp Share on Messenger Close Alex Hern 03.00 EDT 12.21 EST T T The most powerful form of machine learning being used today, called “deep learning”, builds a complex mathematical structure called a neural network based on vast quantities of data. Designed to be analogous to how a human brain works, neural networks themselves were first described in the 1930s. But it’s only in the last three or four years that computers have become powerful enough to use them effectively. <PERSON><PERSON><PERSON> says he thinks it is as big a change for tech as the internet was. “Before internet technologies, if you worked in computer science, networking was some weird thing that weirdos did. And now everyone, regardless of whether they’re an engineer or a software developer or a product designer or a CEO understands how internet connectivity shapes their product, shapes the market, what they could possibly build.” He says that same kind of transformation is going to happen with machine learning. “It ends up being something that everybody can do a little of. They don’t have to do the detailed things, but they need to understand ‘well, wait a minute, maybe we could do this if we had data to learn from.’” One of the nice things about the machine learning community right now is how open it is to sharing ideas and research. When Google made TensorFlow open to anyone to use, it wrote: “By sharing what we believe to be one of the best machine learning toolboxes in the world, we hope to create an open standard for exchanging research ideas and putting machine learning in products”. And it’s not alone in that: every major machine learning implementation is available for free to use and modify, meaning it’s possible to set up a simple machine intelligence with nothing more than a laptop and a web connection. Which is what I did. Robin Sloan
(@robinsloan) So I spent another few hours configuring a server on Amazon’s cloud to do the learning for me. It cost $.70 an hour, but meant that the whole thing was done in about 8 hours. I’m not the only one to play around with the technology. Quietly, starting a few years ago, Google itself has undergone a metamorphosis. The search giant has torn out the guts of some of its biggest services, from image search to voice recognition, and recreated them from the ground up. Now, it wants the rest of the world to follow suit. On 16 June, it announced that it was opening a dedicated Machine Learning group in its Zurich engineering office, the largest collection of Google developers outside of the US, to lead research into three areas: machine intelligence, natural language processing, and machine perception. That is, building systems that can think, listen, and see. Following Sloan’s example, I set my model up to run as an autocomplete engine. I could write the first half-sentance of a theoretical Guardian editorial, and the system gets fed it as an input and asked what it thinks will come next. Don’t like that? Ask it for another response. I tried to use it to read Guardian editorials from a parallel universe. I used “Thursday’s momentous vote to stay in the EU was” as the seed, and tried to get the system to imagine what the rest of the sentence would look like: Thursday’s momentous vote to stay in the EU was more contracts in the 1970s.
Thursday’s momentous vote to stay in the EU was a man of a legal opposition in 2015.
Thursday’s momentous vote to stay in the EU was the discrimination and the first way the failure to declare the government is not a single constitutional conservative policy.
Thursday’s momentous vote to stay in the EU was on the promise of the pronouncements.
It’s terrible. Of course it’s terrible: if I could train a machine to write a convincing Guardian editorial, or even a convincing sentence extract from a Guardian editorial, in two days by copying a readme and fiddling around with complex software which I don’t really understand even after having successfully used it, then my job would be much less secure than it is. Hell, everyone’s jobs would be much less secure than they are.
While the results are unimpressive on the face of it, at the same time, however, they’re … kind of amazing. The specific package I used, called Torch-rnn, is designed for training character-level neural networks. That is, before it’s trained, it doesn’t even know the concept of a word, let alone have a specific vocabulary or understanding of English grammar. Now, I have a model that knows all those things. And it taught itself with nothing more than a huge quantity of Guardian editorials.
It still can’t actually create meaning. That makes sense: a Guardian editorial has meaning in relation to the real world, not as a collection of words existing in its own right. And so to properly train a neural network to write one, you’d also have to feed in information about the world, and then you’ve got less of a weekend project and more of a startup pitch. So it’s not surprising to see the number of startup pitches that do involve “deep learning” skyrocket. My inbox has consistently seen one or two a day for the past year, from an “online personal styling service” which uses deep learning to match people to clothes, to a “knowledge discovery engine” which aims to beat Google at its own game. Where the archetypal startup of 2008 was “x but on a phone” and the startup of 2014 was “uber but for x”, this year is the year of “doing x with machine learning”. And Google seems happy to be leading the way, not only with its own products, but also by making the tools which the rest of the ecosystem is relying on. But why now? Corrado has an answer. “The maths for deep learning was done in the 1980s and 1990s… but until now, computers were too slow for us to understand that the math worked well. “The fact that they’re getting faster and cheaper is part of what’s making this possible.” Right now, he says, doing machine learning yourself is like trying to go online by manually coding a TCP/IP stack.
But that’s going to change. It will get quicker, easier and more effective, and slowly move from something the engineers know about, to something the whole development team know about, then the whole tech industry, and then, eventually, everyone. And when it does, it’s going to change a lot else with it. • Topics Share on Facebook Share on Twitter Share via Email Share on LinkedIn Share on Pinterest Share on Google+ Share on WhatsApp Share on Messenger Reuse this content The Guardian back to top home selected
sections close selected Technology › › Google back to top