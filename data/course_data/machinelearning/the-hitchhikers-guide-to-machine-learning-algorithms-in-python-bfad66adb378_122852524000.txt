The Hitchhiker’s Guide to Machine Learning in Python
Homepage Follow Homepage Home Dev Design Data Learn to code for free Blocked Unblock Follow Following The Hitchhiker’s Guide to Machine Learning in Python Featuring implementation code, instructional videos, and more The Trend The Goal The Breakdown Linear Regression Logistic Regression Decision Trees Support Vector Machines K-Nearest Neighbors Random Forests K-Means Clustering Principal Components Analysis Housekeeping I’m including this simply because this is one of my pet peeves. Trying to utilize someone else’s code only to find that you need three new packages and the code was run in an older version of your language is incredibly frustrating. Linear Regression Anyways, linear regression is a supervised learning algorithm that predicts an outcome based on continuous features. Linear regression is versatile in the sense that it has the ability to be run on a single variable (simple linear regression) or on many features (multiple linear regression). The way it works is by assigning optimal weights to the variables in order to create a line (ax + b) that will be used to predict output. Check out the video below for a more thorough explanation. Now that you’ve got a grasp on the concepts behind linear regression, let’s go ahead and implement it in Python. Logistic Regression Logistic regression is a supervised classification algorithm and therefore is useful for estimating discrete values. It is typically used for predicting the probability of an event using the logistic function in order to get an output between 0 and 1. Now that you’ve got a grasp on the concepts behind logistic regression, let’s implement it in Python. Decision Trees Decision trees are a form of supervised learning that can be used for both classification and regression purposes. In my experience, they are typically utilized for classification purposes. The model takes in an instance and then goes down the tree, testing significant features against a determined conditional statement. Depending on the result, it will go down to the left or right child branch and onward after that. Typically the most significant features in the process will fall closer to the root of the tree. Now that you know a little more about decision trees and how they work, let’s go ahead and implement one in Python. Support Vector Machines This vector is by default and often visualized as being linear, however this doesn’t have to always be the case. The vector can take a nonlinear form as well if the kernel type is changed from the default type of ‘gaussian’ or linear. There’s much more to be said about SVM, so be sure to look into the instructional video below. Now that you know all about support vector machines, let’s go ahead and implement them in Python. K-Nearest Neighbors Now that you’ve got a grasp on the concepts behind the K-Nearest Neighbors algorithm, let’s implement it in Python. Random Forests Now that know all about what’s going on with random forests, time to implement one in Python. K-Means Clustering Now that know more about K-Means clustering and how it works, let’s implement the algorithm in Python. Principal Components Analysis Now that know more about PCA and how it works, let’s implement the algorithm in Python. Wrapping Things Up This tutorial simply scrapes the surface of all the machine learning algorithms being used out there today. With this being said, I hope some of you will find it helpful on your journey to machine learning mastery. Lastly, be sure to subscribe to my weekly data science newsletter below. Thanks for reading! Machine Learning Data Science Python Programming Tech Blocked Unblock Follow Following Conor Dewey Data Scientist & Aspiring Entrepreneur Follow freeCodeCamp Our community publishes stories worth reading on development, design, and data science. Share Get updates Get updates