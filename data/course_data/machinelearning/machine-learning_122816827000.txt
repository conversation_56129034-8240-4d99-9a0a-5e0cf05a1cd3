Guide - Machine Learning | The F# Software Foundation
Toggle navigation Guide - Machine Learning with F# F# is well-suited to machine learning because of its efficient execution, succinct style,
data access capabilities and scalability. F# has been successfully used by some of the most advanced
machine learning teams in the world, including several groups at Microsoft Research. Other guides contain some material related to machine learning: Math and Statistics Data Science Cloud Programming Note that the resources listed below are provided only for educational purposes related to the F# programming language. The F# Software Foundation does not endorse or recommend any commercial products, processes, or services. Therefore, mention of commercial products, processes, or services should not be construed as an endorsement or recommendation. Resources for Machine Learning Tutorials and Introductions Machine Learning Packages Tutorials and Introductions Introductions to different machine learning algorithms with F#: FSML - A machine learning project in F# Gaussian process regression in F# K-Means clustering in F# Simplify data with SVD and Math.NET in F# Recommendation Engine using Math.NET, SVD and F# Setting up F# Interactive for Machine Learning with Large Datasets Random Forests in F# - first cut Nearest Neighbor Classification, Part 1 Nearest Neighbor Classification, Part 2 Decision Tree Classification in F# Naïve Bayes Classification Logistic Regression in F# Support Vector Machine in F#: getting there AdaBoost in F# Support Vector Machines in F# Kaggle/StackOverflow contest field notes F# Data Mining Parallel Programming in F#: Aggregating Data: Particle Swarm Optimization in F# Machine Learning Packages