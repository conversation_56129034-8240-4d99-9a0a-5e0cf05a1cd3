Machine Learning—Wolfram Language Documentation
& Services Wolfram|One Mathematica Development Platform Programming Lab Data Science Platform Finance Platform SystemModeler Enterprise Private Cloud Enterprise Mathematica Wolfram|Alpha Appliance Enterprise Solutions Corporate Consulting Technical Services Wolfram|Alpha Business Solutions Products for Education Data Drop Data Repository Products for Education Wolfram|Alpha Data Drop Data Repository Mobile Apps Services Mobile Apps Services All Products & Services Wolfram Language Revolutionary knowledge-based programming language. Computable Document Format Computation-powered interactive documents. Wolfram Data Framework Semantic framework for real-world data. Wolfram Cloud Central infrastructure for Wolfram's cloud products & services. Wolfram Engine Software engine implementing the Wolfram Language. Wolfram Universal Deployment System Instant deployment across cloud, desktop, mobile, and more. Wolfram Science Technology-enabling science of the computational universe. Wolfram Natural Language Understanding System Knowledge-based, broadly deployed natural language. Wolfram Knowledgebase Curated computable knowledge powering Wolfram|Alpha. All Technologies Engineering, R&D Education Education Web & Software Finance, Statistics & Business Analysis Finance, Statistics & Business Analysis Sciences Trends Sciences Trends All Solutions
& Learning Learning   Need Help? Premium Support All Support & Learning About Work with Us Initiatives   All Company Search Wolfram Language & System Documentation Center GUIDE ActiveClassification ActiveClassificationObject ActivePrediction ActivePredictionObject BayesianMinimization ClassifierFunction ClassifierMeasurements Classify Clip ClusterClassify ClusteringComponents ClusteringTree ConvolutionLayer CountsBy DeleteDuplicates DeleteMissing Dendrogram DimensionReduce DimensionReducerFunction DimensionReduction FeatureExtract FeatureExtraction FeatureExtractor FeatureExtractorFunction FeatureNearest FeatureSpacePlot FeatureTypes FindClusters FindDistribution FindFaces FindFit FindGraphCommunities FindHiddenMarkovStates GatedRecurrentLayer GaussianFilter GroupBy ImageAdjust ImageGraphics ImageIdentify ImageRestyle LanguageIdentify LinearLayer LogisticSigmoid LowpassFilter MeanFilter MeanShiftFilter MovingAverage Nearest NetChain NetGraph NetModel NetTrain NonlinearModelFit PerformanceGoal Predict PredictorFunction PredictorMeasurements RandomSeeding Rescale SequencePredict SequencePredictorFunction SingularValueDecomposition SmoothKernelDistribution SortBy Standardize TextCases TextRecognize TextStructure Threshold TimeGoal Cluster Analysis Statistical Model Analysis Data Transforms & Smoothing Computer Vision Social Network Analysis Machine Learning Predict Classify Classify — Predict — ClassifierFunction — PredictorFunction — ClassifierMeasurements PredictorMeasurements — SequencePredict — SequencePredictorFunction — Nearest    ▪  FindFit    ▪  NonlinearModelFit    ▪  FindHiddenMarkovStates    ▪  ... DimensionReduction — DimensionReduce    ▪  DimensionReducerFunction FeatureExtraction — FeatureExtract    ▪  FeatureExtractorFunction    ▪  FeatureNearest ClusterClassify — FindClusters    ▪  ClusteringTree    ▪  ClusteringComponents FindDistribution — FeatureSpacePlot — Dendrogram — SingularValueDecomposition    ▪  FindGraphCommunities    ▪  SmoothKernelDistribution    ▪  ... BayesianMinimization — ActiveClassification — ActivePrediction — ActiveClassificationObject    ▪  ActivePredictionObject » NetGraph — NetChain    ▪  LinearLayer    ▪  ConvolutionLayer    ▪  GatedRecurrentLayer    ▪  ... NetTrain — NetModel — FeatureExtractor — FeatureTypes — PerformanceGoal — TimeGoal — RandomSeeding — » DeleteMissing — Standardize — Clip    ▪  Rescale    ▪  Threshold    ▪  LogisticSigmoid    ▪  ImageAdjust CountsBy    ▪  GroupBy    ▪  SortBy    ▪  DeleteDuplicates » MovingAverage — GaussianFilter    ▪  MeanFilter    ▪  MeanShiftFilter    ▪  LowpassFilter    ▪  ... ImageIdentify — FindFaces    ▪  TextRecognize    ▪  ImageGraphics LanguageIdentify    ▪  TextStructure    ▪  TextCases    ▪  ... ImageRestyle — Related Guides Related Links Give Top Thank you for your feedback!
Documentation Feedback (optional) (optional) | | Top Products
Services
Services
For Customers
Support
Public Resources
Support
Learning
Public Resources
Company
Connect
2017