Machine Learning Engineer <PERSON><PERSON><PERSON><PERSON> | Udacity
Machine Learning Engineer Nanode<PERSON><PERSON> Make Predictive Models Machine learning represents a key evolution in the fields of computer science, data analysis, software engineering, and artificial intelligence. This program will teach you how to become a machine learning engineer, and apply predictive models to massive data sets in fields like finance, healthcare, education, and more.
Total time between today and graduation day depends on your weekly commitment. On average, our graduates complete this Nanodegree program in
6 months.
1 1 2 2 3 3 Start with a one-week free trial. Nanodegree Program Syllabus In this optional project, you will create decision functions that attempt to predict survival outcomes from the 1912 Titanic disaster based on each passenger’s features, such as sex and age. You will start with a simple algorithm and increase its complexity until you are able to accurately predict the outcomes for at least 80% of the passengers in the provided data. This project will introduce you to some of the concepts of machine learning as you start the Nanodegree program. In this optional project, you will create decision functions that attempt to predict survival outcomes from the 1912 Titanic disaster based on each passenger’s features, such as sex and age. You will start with a simple algorithm and increase its complexity until you are able to accurately predict the outcomes for at least 80% of the passengers in the provided data. This project will introduce you to some of the concepts of machine learning as you start the Nanodegree program. The Boston housing market is highly competitive, and you want to be the best real estate agent in the area. To compete with your peers, you decide to leverage a few basic machine learning concepts to assist you and a client with finding the best selling price for their home. Luckily, you’ve come across the Boston Housing dataset which contains aggregated data on various features for houses in Greater Boston communities, including the median value of homes for each of those areas. Your task is to build an optimal model based on a statistical analysis with the tools available. This model will then used to estimate the best selling price for your client’s home. Intro to Descriptive Statistics Intro to Data Science The Boston housing market is highly competitive, and you want to be the best real estate agent in the area. To compete with your peers, you decide to leverage a few basic machine learning concepts to assist you and a client with finding the best selling price for their home. Luckily, you’ve come across the Boston Housing dataset which contains aggregated data on various features for houses in Greater Boston communities, including the median value of homes for each of those areas. Your task is to build an optimal model based on a statistical analysis with the tools available. This model will then used to estimate the best selling price for your client’s home. CharityML is a fictitious charity organization located in the heart of Silicon Valley that was established to provide financial support for people eager to learn machine learning. After nearly 32,000 letters sent to people in the community, CharityML determined that every donation they received came from someone that was making more than $50,000 annually. To expand their potential donor base, CharityML has decided to send letters to residents of California, but to only those most likely to donate to the charity. With nearly 15 million working Californians, CharityML has brought you on board to help build an algorithm to best identify potential donors and reduce overhead cost of sending mail. Your goal will be evaluate and optimize several different supervised learners to determine which algorithm will provide the highest donation yield while also reducing the total number of letters being sent. CharityML is a fictitious charity organization located in the heart of Silicon Valley that was established to provide financial support for people eager to learn machine learning. After nearly 32,000 letters sent to people in the community, CharityML determined that every donation they received came from someone that was making more than $50,000 annually. To expand their potential donor base, CharityML has decided to send letters to residents of California, but to only those most likely to donate to the charity. With nearly 15 million working Californians, CharityML has brought you on board to help build an algorithm to best identify potential donors and reduce overhead cost of sending mail. Your goal will be evaluate and optimize several different supervised learners to determine which algorithm will provide the highest donation yield while also reducing the total number of letters being sent. A wholesale distributor recently tested a change to their delivery method for some customers, by moving from a morning delivery service five days a week to a cheaper evening delivery service three days a week.Initial testing did not discover any significant unsatisfactory results, so they implemented the cheaper option for all customers. Almost immediately, the distributor began getting complaints about the delivery service change and customers were canceling deliveries — losing the distributor more money than what was being saved. You’ve been hired by the wholesale distributor to find what types of customers they have to help them make better, more informed business decisions in the future. Your task is to use unsupervised learning techniques to see if any similarities exist between customers, and how to best segment customers into distinct categories. A wholesale distributor recently tested a change to their delivery method for some customers, by moving from a morning delivery service five days a week to a cheaper evening delivery service three days a week.Initial testing did not discover any significant unsatisfactory results, so they implemented the cheaper option for all customers. Almost immediately, the distributor began getting complaints about the delivery service change and customers were canceling deliveries — losing the distributor more money than what was being saved. You’ve been hired by the wholesale distributor to find what types of customers they have to help them make better, more informed business decisions in the future. Your task is to use unsupervised learning techniques to see if any similarities exist between customers, and how to best segment customers into distinct categories. In the not-so-distant future, taxicab companies across the United States no longer employ human drivers to operate their fleet of vehicles. Instead, the taxicabs are operated by self-driving agents — known as **smartcabs** — to transport people from one location to another within the cities those companies operate. In major metropolitan areas, such as Chicago, New York City, and San Francisco, an increasing number of people have come to rely on **smartcabs** to get to where they need to go as safely and efficiently as possible. Although **smartcabs** have become the transport of choice, concerns have arose that a self-driving agent might not be as safe or efficient as human drivers, particularly when considering city traffic lights and other vehicles. To alleviate these concerns, your task as an employee for a national taxicab company is to use reinforcement learning techniques to construct a demonstration of a **smartcab** operating in real-time to prove that both safety and efficiency can be achieved. Reinforcement Learning Build a dog breed classifier using a Convolutional Neural Network. Build a dog breed classifier using a Convolutional Neural Network. In this capstone project, you will leverage what you’ve learned throughout the Nanodegree program to solve a problem of your choice by applying machine learning algorithms and techniques. You will first **define** the problem you want to solve and investigate potential solutions and performance metrics. Next, you will **analyze** the problem through visualizations and data exploration to have a better understanding of what algorithms and features are appropriate for solving it.
You will then **implement** your algorithms and metrics of choice, documenting the preprocessing, refinement, and postprocessing steps along the way. Afterwards, you will collect **results** about the performance of the models used, visualize significant quantities, and validate/justify these values. Finally, you will construct **conclusions** about your results, and discuss whether your implementation adequately solves the problem.
In this project, you will update your resume according to the conventions that recruiters expect and get tips on how to best represent yourself to pass the "6 second screen". You will also make sure that your resume is appropriately targeted for the job you’re applying for. We recommend all students update their resumes to show off their newly acquired skills regardless of whether you are looking for a new job soon. In this project, you will update your resume according to the conventions that recruiters expect and get tips on how to best represent yourself to pass the "6 second screen". You will also make sure that your resume is appropriately targeted for the job you’re applying for. We recommend all students update their resumes to show off their newly acquired skills regardless of whether you are looking for a new job soon. For this project, you will be given five technical interviewing questions on a variety of topics discussed in the technical interviewing course. You should write up a clean and efficient answer in Python, as well as a text explanation of the efficiency of your code and your design choices. A qualified reviewer will look over your answer and give you feedback on anything that might be awesome or lacking—is your solution the most efficient one possible? Are you doing a good job of explaining your thoughts? Is your code elegant and easy to read? For this project, you will be given five technical interviewing questions on a variety of topics discussed in the technical interviewing course. You should write up a clean and efficient answer in Python, as well as a text explanation of the efficiency of your code and your design choices. A qualified reviewer will look over your answer and give you feedback on anything that might be awesome or lacking—is your solution the most efficient one possible? Are you doing a good job of explaining your thoughts? Is your code elegant and easy to read? Student Reviews Why Take This Nanodegree Program? This program will equip you with key skills that will prepare you to fill roles with companies seeking machine learning experts (or to introduce machine learning techniques to their organizations). Machine learning is literally everywhere, and is often at work even when we don’t realize it. Google Translate, Siri, and Facebook News Feeds are just a few popular examples of machine learning’s omnipresence. The ability to develop machines and systems that automatically improve, puts machine learning at the absolute forefront of virtually any field that relies on data. Job-ready project portfolio Personalized feedback on projects Coach-supported forums Career guidance (interview, resume, etc.) Access to course materials Verified Nanodegree Credential Best-in-class courses taught by expert instructors
A Nanodegree program is an innovative curriculum path that is outcome-based and career-oriented.
Every program has a clear end-goal, and the ideal path to get you there. Courses are built with
industry leaders like Google, AT&T, and Facebook, and are taught by leading subject matter
experts. Students benefit from personalized mentoring and project-review throughout, and have
regular access to instructors and course managers through moderated forums.
Graduates earn an industry-recognized credential and benefit from extensive career support. The
ultimate goal of a Nanodegree program is to teach the skills you need, for the career you want,
so you can build the life you deserve.
Student Success Story Web Solutions Engineer, Google Enrollment Learning with Udacity means getting you exactly where you want to be in your career. Most Popular Nanodegree Program Our flagship Nanodegree programs represent career-track education at its most innovative. Every program is comprised of these core features: Master cutting-edge skills sought by leading companies Rigorous, timely project and code reviews Build an optimized portfolio, earn a recognized credential Connect directly to exclusive hiring partners Graduate in 12 months, get a 50% tuition refund Nanodegree Plus If your goal is to secure a specific role in a specific field, we have Nanodegree Plus—all the features of the Nanodegree program, plus a job guarantee. Master cutting-edge skills sought by leading companies Rigorous, timely project and code reviews Build an optimized portfolio, earn a recognized credential Connect directly to exclusive hiring partners Prerequisites and Requirements Prior to entering the Machine Learning Engineer Nanodegree program, the student should have the following knowledge: Strings, numbers, and variables Statements, operators, and expressions Lists, tuples, and dictionaries Conditions, loops Procedures, objects, modules, and libraries Troubleshooting and debugging Research & documentation Problem solving Populations, samples Mean, median, mode Standard error Variation, standard deviations Normal distribution Derivatives Integrals Series expansions Matrix operations through eigenvectors and eigenvalues Program Leads Course Developer Course Developer Course Developer Instructor Instructor Instructor Start with a one-week free trial. Get Notified Get notified when the Machine Learning Engineer Nanodegree program launches. Thanks for your interest! We'll be in touch soon. Android Basics Android Developer Become an iOS Developer Business Analyst Data Analyst Data Foundations Front-End Web Developer Full Stack Web Developer Intro to Programming React Artificial Intelligence Deep Learning Foundations Digital Marketing Machine Learning Engineer Robotics Self-Driving Car Engineer VR Developer Catalog Career Resource Center Hiring Partners Student Success Udacity Connect Udacity Talks Scholarships Nanodegree Plus Veterans Georgia Tech Udacity Self-Driving Car About Blog In the News Jobs Mobile Udacity Intersect Udacity for Business Corporate Training Hire Graduates Contact Us Help and FAQ Service Status Course Guides Tech Requirements Legal & Privacy Regulatory Information Site Map © 2011–2017 Udacity, Inc. © 2011–2017 Udacity, Inc. Legal & Privacy Regulatory Information Site Map © 2011–2017 Udacity, Inc. Udacity is not an accredited university and we don't confer degrees. Udacity 现已提供中文版本！ A Udacity tem uma página em português para você! There's a local version of Udacity for you! Sprechen Sie Deutsch? 将此设置为 Udacity 默认主页 Tornar esta a página padrão da Udacity Always make this my Udacity homepage 前往优达学城中文网站 Ir para a página brasileira Go to Indian Site Zu de.udacity.com