Apple just put machine learning in your pocket | HuffPost
EDITION <PERSON><PERSON><PERSON>, Contributor Editor in Chief, All Turtles Apple just put machine learning in your pocket 09/14/2017 06:43 pm ET <PERSON>’s hyperbole may be warranted, indeed. Think of what the iPod did for music. Think of what the iPhone camera did for photography. Now think of what the iPhone X will do for machine learning or, rather, what iPhone X developers and customers will do with machine learning.
It starts with what’s inside the iPhone X. 600 billion operations per second Apple touted the prowess of its new hardware and software by unveiling just two applications: Face ID, facial recognition to unlock the new iPhone X and to make purchases with Apple Pay, and
and animated emoji’s — so-called animojis. The company also described how how facial recognition can work with augmented reality apps. But don’t let these modest examples disappoint you. With Apple (and other device makers), the real magic often comes from the developer community. Hinting that other third party applications are on the horizon, Apple’s vice president of worldwide marketing <PERSON> noted that Face ID can work with existing apps like Mint, 1Password, and E*Trade, which currently offer Touch ID to execute transactions with a finger print. The atomization of machine learning Technology improvements transfer the power to create, share, and enjoy from from the few to the many. Rip-Mix-Burn, streaming, and playlists ended the hegemony of record companies and radio. VCRs, video cameras, and YouTube broke Hollywood’s grip on the movie industry. Desktop publishing, blogging, and a little help from Craiglist, eroded the localized monopolies of newspapers. Admittedly, I’m simplifying things to make a point.
For instance, I spoke recently with an AI entrepreneur who built a mobile app using very inexpensive software from
Google, Amazon, IBM Watson, as well as open source deep learning tools. This team of three required very little investment and created something that it is already being used by nearly 100,000 people after only a few months. I can only guess what Apple’s new iPhone may make possible for them. The power of X The iPhone X represents a significant push of machine learning — it’s creation, distribution, and consumption — from the center to the edge, to the masses, to the edglings. What will developers build for these devices? What everyday problems will they solve? If we are to believe Tim Cook’s hyperbole, then we need to ask, if it took 10 years for the iPhone to advance into an machine learning supercomputer, what will the next 10 years look like? I can’t wait to find out. CONVERSATIONS
FROM OUR PARTNERS
MULTIMEDIA
Close