Machine Learning | Coursera
Toggle navigation Navigation open Navigation closed Browse Search For Enterprise Log In Sign Up Machine Learning Enroll Machine Learning Enroll Starts Sep 18 Machine Learning Machine Learning Stanford University    Stanford University    Language ,
How To Pass Pass all graded assignments to complete the course. User Ratings See what learners said Syllabus 5 videos ,
9 readings Welcome to Machine Learning! Machine Learning Honor Code Welcome What is Machine Learning? What is Machine Learning? How to Use Discussion Forums Supervised Learning Supervised Learning Unsupervised Learning Unsupervised Learning Who are Mentors? Get to Know Your Classmates Frequently Asked Questions Lecture Slides Introduction 7 videos ,
8 readings Model Representation Model Representation Cost Function Cost Function Cost Function - Intuition I Cost Function - Intuition I Cost Function - Intuition II Cost Function - Intuition II Gradient Descent Gradient Descent Gradient Descent Intuition Gradient Descent Intuition Gradient Descent For Linear Regression Gradient Descent For Linear Regression Lecture Slides Linear Regression with One Variable 6 videos ,
7 readings ,
1 practice quiz Matrices and Vectors Matrices and Vectors Addition and Scalar Multiplication Addition and Scalar Multiplication Matrix Vector Multiplication Matrix Vector Multiplication Matrix Matrix Multiplication Matrix Matrix Multiplication Matrix Multiplication Properties Matrix Multiplication Properties Inverse and Transpose Inverse and Transpose Lecture Slides Linear Algebra 8 videos ,
16 readings Setting Up Your Programming Assignment Environment Installing MATLAB Installing Octave on Windows Installing Octave on Mac OS X (10.10 Yosemite and 10.9 Mavericks and Later) Installing Octave on Mac OS X (10.8 Mountain Lion and Earlier) Installing Octave on GNU/Linux More Octave/MATLAB resources Multiple Features Multiple Features Gradient Descent for Multiple Variables Gradient Descent For Multiple Variables Gradient Descent in Practice I - Feature Scaling Gradient Descent in Practice I - Feature Scaling Gradient Descent in Practice II - Learning Rate Gradient Descent in Practice II - Learning Rate Features and Polynomial Regression Features and Polynomial Regression Normal Equation Normal Equation Normal Equation Noninvertibility Normal Equation Noninvertibility Working on and Submitting Programming Assignments Programming tips from Mentors Lecture Slides Linear Regression with Multiple Variables 6 videos ,
1 reading Basic Operations Moving Data Around Computing on Data Plotting Data Control Statements: for, while, if statement Vectorization Lecture Slides Linear Regression Octave/Matlab Tutorial 7 videos ,
8 readings Classification Classification Hypothesis Representation Hypothesis Representation Decision Boundary Decision Boundary Cost Function Cost Function Simplified Cost Function and Gradient Descent Simplified Cost Function and Gradient Descent Advanced Optimization Advanced Optimization Multiclass Classification: One-vs-all Multiclass Classification: One-vs-all Lecture Slides Logistic Regression 4 videos ,
5 readings The Problem of Overfitting The Problem of Overfitting Cost Function Cost Function Regularized Linear Regression Regularized Linear Regression Regularized Logistic Regression Regularized Logistic Regression Lecture Slides Logistic Regression Regularization 7 videos ,
6 readings Non-linear Hypotheses Neurons and the Brain Model Representation I Model Representation I Model Representation II Model Representation II Examples and Intuitions I Examples and Intuitions I Examples and Intuitions II Examples and Intuitions II Multiclass Classification Multiclass Classification Lecture Slides Multi-class Classification and Neural Networks Neural Networks: Representation 8 videos ,
8 readings Cost Function Cost Function Backpropagation Algorithm Backpropagation Algorithm Backpropagation Intuition Backpropagation Intuition Implementation Note: Unrolling Parameters Implementation Note: Unrolling Parameters Gradient Checking Gradient Checking Random Initialization Random Initialization Putting It Together Putting It Together Autonomous Driving Lecture Slides Neural Network Learning Neural Networks: Learning 7 videos ,
7 readings Deciding What to Try Next Evaluating a Hypothesis Evaluating a Hypothesis Model Selection and Train/Validation/Test Sets Model Selection and Train/Validation/Test Sets Diagnosing Bias vs. Variance Diagnosing Bias vs. Variance Regularization and Bias/Variance Regularization and Bias/Variance Learning Curves Learning Curves Deciding What to Do Next Revisited Deciding What to do Next Revisited Lecture Slides Regularized Linear Regression and Bias/Variance Advice for Applying Machine Learning 5 videos ,
3 readings Prioritizing What to Work On Prioritizing What to Work On Error Analysis Error Analysis Error Metrics for Skewed Classes Trading Off Precision and Recall Data For Machine Learning Lecture Slides Machine Learning System Design 6 videos ,
1 reading Optimization Objective Large Margin Intuition Mathematics Behind Large Margin Classification Kernels I Kernels II Using An SVM Lecture Slides Support Vector Machines Support Vector Machines 5 videos ,
1 reading Unsupervised Learning: Introduction K-Means Algorithm Optimization Objective Random Initialization Choosing the Number of Clusters Lecture Slides Unsupervised Learning 7 videos ,
1 reading Motivation I: Data Compression Motivation II: Visualization Principal Component Analysis Problem Formulation Principal Component Analysis Algorithm Reconstruction from Compressed Representation Choosing the Number of Principal Components Advice for Applying PCA Lecture Slides K-Means Clustering and PCA Principal Component Analysis 8 videos ,
1 reading Problem Motivation Gaussian Distribution Algorithm Developing and Evaluating an Anomaly Detection System Anomaly Detection vs. Supervised Learning Choosing What Features to Use Multivariate Gaussian Distribution Anomaly Detection using the Multivariate Gaussian Distribution Lecture Slides Anomaly Detection 6 videos ,
1 reading Problem Formulation Content Based Recommendations Collaborative Filtering Collaborative Filtering Algorithm Vectorization: Low Rank Matrix Factorization Implementational Detail: Mean Normalization Lecture Slides Anomaly Detection and Recommender Systems Recommender Systems 6 videos ,
1 reading Learning With Large Datasets Stochastic Gradient Descent Mini-Batch Gradient Descent Stochastic Gradient Descent Convergence Online Learning Map Reduce and Data Parallelism Lecture Slides Large Scale Machine Learning 5 videos ,
1 reading Problem Description and Pipeline Sliding Windows Getting Lots of Data and Artificial Data Ceiling Analysis: What Part of the Pipeline to Work on Next Lecture Slides Summary and Thank You Application: Photo OCR Enroll FAQs When will I have access to the lectures and assignments? What if I need additional time to complete the course? What is the refund policy? Is financial aid available? How It Works Coursework Each course is like an interactive textbook, featuring pre-recorded videos, quizzes and projects. Help from Your Peers Connect with thousands of other learners and debate ideas, discuss course material,
and get help mastering concepts. Certificates Earn official recognition for your work, and share your success with friends,
colleagues, and employers. Creators Ratings and Reviews 48,502 RS If you want to learn machine learning, this is the course to take. Andrew Ng explains the concepts of machine learning in a very easy to understand way, and the assignments provide a lot of hands on training that helps to reinforce who you just learned. 孙 老师课程讲得非常好 HF This was my first introduction to Machine Learning, a topic I previously thought of as impossibly complicated. This course has been incredibly insightful (and as concise as possible). The organisation and pace of the course made it easy to follow (and you can simply pause whenever needed). The programming coursework had most of the "padding code" already provided, letting the student focus on the machine learning core of the assignment. I managed to do the course by allocating a bit of time after work and during the weekends and I highly recommend it! I am now looking forward to starting the new "Deep Learning" course.
PR Great Course, really detailed, well prepared and with all the math background you need to tackle ML problems. I really appreciated the effort to set up a correct logical approach to problems, their evaluation and performance analysis. Great also the commercial approach, to put you in the right direction for real business applications.
12,555 Enroll Share You May Also Like University of Washington 1 course 1 course University of Washington View course View course University of Washington 1 course 1 course University of Washington View course View course deeplearning.ai 1 course 1 course deeplearning.ai View course View course University of Washington 1 course 1 course University of Washington View course View course deeplearning.ai 1 course 1 course deeplearning.ai View course View course Coursera Coursera Coursera provides universal access to the world’s best education, partnering with top universities and organizations to offer courses online. © 2017 Coursera Inc. All rights reserved. Coursera About Leadership Careers Catalog Certificates Degrees For Business For Government Community Partners Mentors Translators Developers Beta Testers Connect Blog Facebook LinkedIn Twitter Google+ Tech Blog More Terms Privacy Help Accessibility Press Contact Directory Affiliates