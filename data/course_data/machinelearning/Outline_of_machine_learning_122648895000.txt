Outline of machine learning - Wikipedia
Outline of machine learning Contents 1 2 Branches of machine learning 2.1 Subfields 2.2 Cross-disciplinary fields 3 Machine learning hardware 4 Machine learning tools 4.1 Proprietary frameworks 4.2 Open source frameworks 4.3 Machine learning libraries 5 Machine learning methods 5.1 Supervised learning 5.1.1 Artificial neural network 5.1.2 Bayesian 5.1.3 Decision tree 5.1.4 Linear classifier 5.2 Unsupervised learning 5.2.1 Artificial neural network 5.2.2 Association rule learning 5.2.3 Hierarchical clustering 5.2.4 Cluster analysis 5.2.5 Anomaly detection 5.3 Semi-supervised learning 5.4 Reinforcement learning 5.5 Deep learning 5.6 Others 6 Applications of machine learning 7 Machine learning problems and tasks 8 Machine learning research 9 History of machine learning 10 Machine learning projects 11 Machine learning organizations 12 Machine learning venues 12.1 Machine learning conferences and workshops 12.2 Machine learning journals 13 Persons influential in machine learning 14 See also 15 Further reading 16 References 17 External links [ ] Branches of machine learning [ ] Subfields [ ] Grammar induction Meta learning Cross-disciplinary fields [ ] Adversarial machine learning Predictive analytics Quantum machine learning Robot learning Machine learning hardware [ ] Graphics processing unit Tensor processing unit Vision processing unit Machine learning tools [ ] Comparison of deep learning software/Resources Proprietary frameworks [ ] Amazon Machine Learning Azure ML Studio Microsoft Cognitive Toolkit Open source frameworks [ ] Apache Singa Caffe H2O MLPACK TensorFlow Torch Machine learning libraries [ ] Deeplearning4j Theano Scikit-learn Machine learning methods [ ] CCA Factor analysis Projection pursuit Sammon mapping Boosting AdaBoost Random Forest Logistic regression Linear regression Stepwise regression Ridge regression Elastic net Naive Bayes classifier Binary classifier Linear classifier Hierarchical classifier Supervised learning [ ] AODE Apriori algorithm Eclat algorithm Case-based reasoning Gaussian process regression Gene expression programming Inductive logic programming Instance-based learning Lazy learning Learning Automata Learning Vector Quantization Logistic Model Tree Nearest Neighbor Algorithm Analogical modeling Support vector machines Random Forests Boosting (meta-algorithm) Ordinal classification Conditional Random Field ANOVA Quadratic classifiers k-nearest neighbor SPRINT Naive Bayes Hidden Markov models Artificial neural network [ ] Autoencoder Backpropagation Boltzmann machine Convolutional neural network Deep learning Hopfield network Multilayer perceptron Perceptron Restricted Boltzmann machine Spiking neural network Bayesian [ ] Bayesian knowledge base Naive Bayes Gaussian Naive Bayes Multinomial Naive Bayes Decision tree [ ] C4.5 algorithm C5.0 algorithm Decision stump Conditional decision tree ID3 algorithm Random forest SLIQ Linear classifier [ ] Fisher's linear discriminant Linear regression Logistic regression Multinomial logistic regression Naive Bayes classifier Perceptron Support vector machine Unsupervised learning [ ] Expectation-maximization algorithm Vector Quantization Generative topographic map Information bottleneck method Artificial neural network [ ] Extreme learning machine Logic learning machine Self-organizing map Association rule learning [ ] Apriori algorithm Eclat algorithm FP-growth algorithm Hierarchical clustering [ ] Single-linkage clustering Conceptual clustering Cluster analysis [ ] BIRCH DBSCAN Expectation-maximization (EM) Fuzzy clustering Hierarchical Clustering K-means algorithm K-means clustering K-medians Mean-shift OPTICS algorithm Anomaly detection [ ] Anomaly detection Local outlier factor Semi-supervised learning [ ] Generative models Low-density separation Graph-based methods Co-training Reinforcement learning [ ] Temporal difference learning Q-learning Learning Automata Deep learning [ ] Deep belief networks Hierarchical temporal memory Stacked Auto-Encoders Others [ ] Data Pre-processing Online machine learning Applications of machine learning [ ] Biomedical informatics Computer vision Data mining Email filtering Automatic summarization Automatic taxonomy construction Dialog system Grammar checker Handwriting recognition Optical character recognition Speech recognition Machine translation Question answering Speech synthesis Text simplification Facial recognition system Handwriting recognition Image recognition Optical character recognition Speech recognition Recommendation system Search engine Machine learning problems and tasks [ ] Anomaly detection Association rules Bias-variance dilemma Classification Clustering Empirical risk minimization Feature engineering Feature learning Learning to rank Occam learning Online learning PAC learning Regression Reinforcement Learning Semi-supervised learning Statistical learning Bayesian network Unsupervised learning VC theory Machine learning research [ ] List of artificial intelligence projects List of datasets for machine learning research History of machine learning [ ] Timeline of machine learning Machine learning projects [ ] DeepMind Google Brain Machine learning organizations [ ] Knowledge Engineering and Machine Learning Group Machine learning venues [ ] Machine learning conferences and workshops [ ] Artificial Intelligence and Security (AISec) (co-located workshop with CCS) ECML PKDD Machine learning journals [ ] Machine Learning Neural Computation Persons influential in machine learning [ ] Alberto Broggi Andrei Knyazev Andrew McCallum Andrew Ng Armin B. Cremers Ayanna Howard Barney Pell Ben Goertzel Ben Taskar Bernhard Schölkopf Brian D. Ripley Christopher G. Atkeson Corinna Cortes Demis Hassabis Douglas Lenat Eric Xing Ernst Dickmanns Hans-Peter Kriegel Hartmut Neven Heikki Mannila Jacek M. Zurada Jaime Carbonell Jerome H. Friedman John D. Lafferty Julie Beth Lovins Jürgen Schmidhuber Karl Steinbuch Katia Sycara Lise Getoor Luca Maria Gambardella Léon Bottou Marcus Hutter Mehryar Mohri Michael Collins Michael I. Jordan Michael L. Littman Nando de Freitas Ofer Dekel Oren Etzioni Pedro Domingos Peter Flach Pierre Baldi Pushmeet Kohli Ray Kurzweil Rayid Ghani Ross Quinlan Salvatore J. Stolfo Sebastian Thrun Selmer Bringsjord Sepp Hochreiter Shane Legg Stephen Muggleton Steve Omohundro Tom M. Mitchell Trevor Hastie Vasant Honavar Yasuo Matsuyama Yoshua Bengio Zoubin Ghahramani See also [ ] Machine learning portal Outline of computer vision Outline of natural language processing Outline of robotics Further reading [ ] References [ ] ^     ^     ^ http://www.learningtheory.org/ External links [ ] sister projects Data Science: Data to Insights from MIT (machine learning) International Machine Learning Society v t e Culture and the arts Geography and places Health and fitness History and events Mathematics and logic Natural and physical sciences People and self Philosophy and thinking Religion and belief systems Society and social sciences Technology and applied sciences Machine learning Artificial intelligence Data mining Navigation menu Personal tools Not logged in Talk Contributions Create account Log in Namespaces Article Article Talk Talk Variants
Views Read Read Edit Edit View history View history More More
Navigation Main page Contents Featured content Current events Random article Donate to Wikipedia Wikipedia store Interaction Help About Wikipedia Community portal Recent changes Contact page Tools What links here Related changes Upload file Special pages Permanent link Page information Wikidata item Cite this page Print/export Create a book Download as PDF Printable version In other projects Wikimedia Commons Languages 한국어 Tiếng Việt Edit links
This page was last edited on 15 August 2017, at 10:11. Privacy policy About Wikipedia Disclaimers Contact Wikipedia Developers Cookie statement Mobile view