The evolution of machine learning
|
TechCrunch
Menu Search Follow Us Facebook Instagram Twitter Youtube Flipboard LinkedIn Google+ RSS More Youtube Flipboard LinkedIn Google+ RSS Let us know. Channels Startups Mobile Gadgets Enterprise Social Europe Asia Crunch Network Unicorn Leaderboard Gift Guides Apps Bullish Crunch Report Disrupt NY 2017 Judah vs the Machines Gadgets Interviews News Reviews TC Features TechCrunch Events Disrupt Startup Battlefield Battlefield Africa Battlefield Australia Crunchies Meetups International City Events Hackathon Sessions Include TechCrunch Store News About Mobile World Congress CES Amazon Tesla Microsoft Startups Mobile Gadgets Enterprise Social Europe Message Us Search Search Hi!
You are about to activate our Facebook Messenger news bot. Once subscribed, the bot will send you a digest of trending stories once a day. You can also customize the types of stories it sends you.
Click on the button below to subscribe and wait for a new Facebook message from the TC Messenger news bot.
Disrupt SF A typical day for researchers on Google’s Brain Team The AI ecosystem to be on display at Disrupt SF Facebook is the latest tech giant to hunt for AI talent in Canada Browse more... Twitter says a now-fixed bug allowed ad campaigns to target users with derogatory terms A typical day for researchers on Google’s Brain Team US election agency seeks views on rule change for digital ad platforms Browse more... Alphabet is reportedly mulling a $1B investment in Lyft Africa Roundup: eBay expands, Google CEO visits Lagos,
Ghana enters space Crunch Report | China’s Central Bank Puts a Ban on ICOs Browse more... Twitter says a now-fixed bug allowed ad campaigns to target users with derogatory terms US election agency seeks views on rule change for digital ad platforms Europe says ‘all options on table’ for taxing tech giants Browse more... Facebook and Microsoft collaborate to simplify conversions from PyTorch to Caffe2 Google’s Transformer solves a tricky problem in machine translation Documents detail DeepMind’s plan to apply AI to NHS data in 2015 Browse more... Crunch Network The evolution of machine learning
Catherine Dong Contributor They’re pouring resources and attention into convincing the world that the machine intelligence revolution is arriving now. They tout deep learning, in particular, as the breakthrough driving this transformation and powering new self-driving cars, virtual assistants and more. Software engineers and data scientists working with machine learning still use many of the same algorithms and engineering tools they did years ago. Large tech companies have recently started to use their own centralized platforms for machine learning engineering, which more cleanly tie together the previously scattered workflows of data scientists and engineers. What goes into a machine learning sandwich Machine learning engineering happens in three stages — data processing, model building and deployment and monitoring. In the middle we have the meat of the pipeline, the model, which is the machine learning algorithm that learns to predict given input data. That model is where “deep learning” would live. Deep learning is a subcategory of machine learning algorithms that use multi-layered neural networks to learn complex relationships between inputs and outputs. The more layers in the neural network, the more complexity it can capture. Traditional statistical machine learning algorithms (i.e. ones that do not use deep neural nets) have a more limited capacity to capture information about training data. But these more basic machine learning algorithms work well enough for many applications, making the additional complexity of deep learning models often superfluous. So we still see software engineers using these traditional models extensively in machine learning engineering — even in the midst of this deep learning craze. But the bread of the sandwich process that holds everything together is what happens before and after training the machine learning model. The first stage involves cleaning and formatting vast amounts of data to be fed into the model. The last stage involves careful deployment and monitoring of the model. We found that most of the engineering time in AI is not actually spent on building machine learning models — it’s spent preparing and monitoring those models. The meat of machine learning — and avoiding exotic flavors Despite the focus on deep learning at the big tech company AI research labs, most applications of machine learning at these same companies do not rely on neural networks and instead use traditional machine learning models. The most common models include linear/logistic regression, random forests and boosted decision trees. These are the models behind, among other services tech companies use, friend suggestions, ad targeting, user interest prediction, supply/demand simulation and search result ranking. And some of the tools engineers use to train these models are similarly well-worn. One of the most commonly used machine learning libraries is scikit-learn, which was released a decade ago (although Google’s TensorFlow is on the rise). With traditional machine learning models, the time engineers spend on model training and tuning is relatively short — usually just a few hours. Ultimately, if the accuracy improvements that deep learning can achieve are modest, the need for scalability and development speed outweighs their value. Attempting to stick it all together — tools from data to deployment The first stage in the machine learning pipeline — data collection and processing — illustrates this. While big companies certainly have big data, data scientists or engineers must clean the data to make it useful — verify and consolidate duplicates from different sources, normalize metrics, design and prove features. At most companies, engineers do this using a combination SQL or Hive queries and Python scripts to aggregate and format up to several million data points from one or more data sources. This often takes several days of frustrating manual labor. Some of this is likely repetitive work, because the process at many companies is decentralized — data scientists or engineers often manipulate data with local scripts or Jupyter Notebooks. However, traditional unit tests — the backbone of traditional software testing — don’t really work with machine learning models, because the correct output of machine learning models isn’t known beforehand. After all, the purpose of machine learning is for the model to learn to make predictions from data without the need for an engineer to specifically code any rules. So instead of unit tests, engineers take a less structured approach: They manually monitor dashboards and program alerts for new models. And shifts in real-world data may make trained models less accurate, so engineers re-train production models on fresh data on a daily to monthly basis, depending on the application. But a lack of machine learning-specific support in the existing engineering infrastructure can create a disconnect between models in development and models in production — normal code is updated much less frequently. Many engineers still rely on rudimentary methods of deploying models to production, like saving a serialized version of the trained model or model weights to a file. Engineers sometimes need to rebuild model prototypes and parts of the data pipeline in a different language or framework, so they work on production infrastructure. Any incompatibility from any stage of the machine learning development process — from data processing to training to deployment to production infrastructure — can introduce error. Making it presentable — the road forward Despite all the emphasis big tech companies have placed on enhancing their products with machine learning, at most companies there are still major challenges and inefficiencies in the process. They still use traditional machine learning models instead of more-advanced deep learning, and still depend on a traditional infrastructure of tools poorly suited to machine learning. Fortunately, with the current focus on AI at these companies, they are investing in specialized tools to make machine learning work better. With these internal tools, or potentially with third-party machine learning platforms that are able to integrate tightly into their existing infrastructures, organizations can realize the potential of AI. A special thank you to Irving Hsu, David Eng, Gideon Mann and the Bloomberg Beta team for their insights. Crunchbase 1998 Google is a multinational corporation that is specialized in internet-related services and products. The company’s product portfolio includes Google Search, which provides users with access to information online; Knowledge Graph that allows users to search for things, people, or places as well as builds systems recognizing speech and understanding natural language; Google Now, which provides information … 2015 Alphabet Inc. is the holding company for Google and several Google entities, including Google X, Google Ventures, Google Capital, Calico, and its Life Sciences efforts.
On Monday, August 10th, 2015, CEO Larry Page announced the operational restructuring effort for Alphabet Inc. to replace Google Inc. as the official publicly-traded entity. He announced that all shares of Google would automatically … Larry Page
Newsletter Subscriptions
Get the top tech stories of the day delivered to your inbox Get a weekly recap of the biggest tech stories The latest startup funding announcements Crunch Report Patreon is raising a Series C at $450M | Crunch Report Featured Stories Latest From UK to criminalize re-identifying anonymized personal data CrunchBoard Writer/Editor, Electronics at Consumer Reports (Yonkers, NY 10703, United States) Chief of Staff at WayBetter (New York, NY, United States) Software Engineer
at Wizeline (Guadalajara, México) Senior Producer at Wooga (Berlin, Germany) Sr Engineering Manager - Authentication at Target (Brooklyn Park, MN, United States) News Video Events Crunchbase TechCrunch Store Staff Contact Us Advertise With Us Event & Editorial Calendar Send Us A Tip Activations Blog China Europe Japan Follow TechCrunch Facebook Facebook Twitter Twitter Google+ Google+ LinkedIn LinkedIn Youtube Youtube Pinterest Pinterest Tumblr Tumblr Instagram Instagram StumbleUpon StumbleUpon Feed Feed TechCrunch Apps iOS iOS Android Android Windows 8 Windows 8 Subscribe to
Latest headlines delivered to you daily
© 2013-2017 Oath Inc. All rights reserved.
News Startups Mobile Gadgets Enterprise Social Europe Asia Crunch Network Unicorn Leaderboard Gift Guides All Galleries All Timelines Videos Apps Bullish Crunch Report Disrupt NY 2017 Judah vs the Machines All Shows All Videos Events Disrupt Startup Battlefield Battlefield Africa Battlefield Australia Crunchies Meetups International City Events Hackathon Sessions Include TechCrunch Store All Events Crunchbase