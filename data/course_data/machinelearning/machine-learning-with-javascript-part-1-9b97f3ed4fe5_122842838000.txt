Machine Learning with JavaScript : Part 1 – Hacker Noon
Homepage Follow Homepage Home Newsletter Top Stories AI
VC ICO Javascript Origin Story Latest in Tech Blocked Unblock Follow Following Machine Learning with JavaScript : Part 1 And you thought it wasn’t easy It has been around for quite a while now, with Google going from mobile-first strategy to AI-first. Why JavaScript is not mentioned with ML? Libraries are usually made for Python. (The JS people are not behind) There are a handful of libraries in JavaScript with pre-made Machine Learning algorithms, such as Linear Regression, SVMs, Naive-<PERSON><PERSON>’s, et cetera. Here are a few of them, Pretty neat, eh? Now that our data has successfully been dressed, it’s time to train our model. Here’s how it looks: (Note that I am using Node.js’ readline utility) And here’s the code for adding reading user input: If you followed the steps, this is how your index.js should look: Congratulations. You just trained your first Linear Regression Model in JavaScript. (Did you notice the speed?) Machine Learning ML Js JavaScript Ml With Js Blocked Unblock Follow Following Abhishek Soni Follow Hacker Noon how hackers start their afternoons. Share Get updates Get updates