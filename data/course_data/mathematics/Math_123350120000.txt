Mathematics Standards			| Common Core State Standards Initiative
Home Development Process Frequently Asked Questions Myths vs. Facts Branding Guidelines Contact What Parents Should Know Standards in Your State English Language Arts Standards Mathematics Standards Key Shifts in English Language Arts Key Shifts in Mathematics Statements of Support Mathematics Standards For more than a decade, research studies of mathematics education in high-performing countries have concluded that mathematics education in the United States must become substantially more focused and coherent in order to improve mathematics achievement in this country. To deliver on this promise, the mathematics standards are designed to address the problem of a curriculum that is “a mile wide and an inch deep.” These new standards build on the best of high-quality math standards from states across the country. They also draw on the most important international models for mathematical practice, as well as research and input from numerous sources, including state departments of education, scholars, assessment developers, professional organizations, educators, parents and students, and members of the public. The math standards provide clarity and specificity rather than broad general statements. They endeavor to follow the design envisioned by <PERSON> and <PERSON> (2002), by not only stressing conceptual understanding of key ideas, but also by continually returning to organizing principles such as place value and the laws of arithmetic to structure those ideas. In addition, the “sequence of topics and performances” that is outlined in a body of math standards must respect what is already known about how students learn. <PERSON> <PERSON><PERSON> (2007) points out, developing “sequenced obstacles and challenges for students…absent the insights about meaning that derive from careful study of learning, would be unfortunate and unwise.” Therefore, the development of the standards began with research-based learning progressions detailing what is known today about how students’ mathematical knowledge, skill, and understanding develop over time. The knowledge and skills students need to be prepared for mathematics in college, career, and life are woven throughout the mathematics standards. They do not include separate Anchor Standards like those used in the ELA/literacy standards. The Common Core concentrates on a clear set of math skills and concepts. Students will learn concepts in a more organized way both during the school year and across grades. The standards encourage students to solve real-world problems. Understanding Mathematics These standards define what students should understand and be able to do in their study of mathematics. But asking a student to understand something also means asking a teacher to assess whether the student has understood it. But what does mathematical understanding look like? One way for teachers to do that is to ask the student to justify, in a way that is appropriate to the student’s mathematical maturity, why a particular mathematical statement is true or where a mathematical rule comes from. Mathematical understanding and procedural skill are equally important, and both are assessable using mathematical tasks of sufficient richness. Standards for Mathematical Practice How to read the grade level standards Introduction Counting & Cardinality Operations & Algebraic Thinking Number & Operations in Base Ten Measurement & Data Geometry Introduction Operations & Algebraic Thinking Number & Operations in Base Ten Measurement & Data Geometry Introduction Operations & Algebraic Thinking Number & Operations in Base Ten Measurement & Data Geometry Introduction Operations & Algebraic Thinking Number & Operations in Base Ten Number & Operations—Fractions¹ Measurement & Data Geometry Introduction Operations & Algebraic Thinking Number & Operations in Base Ten¹ Number & Operations—Fractions¹ Measurement & Data Geometry Introduction Operations & Algebraic Thinking Number & Operations in Base Ten Number & Operations—Fractions Measurement & Data Geometry Introduction Ratios & Proportional Relationships The Number System Expressions & Equations Geometry Statistics & Probability Introduction Ratios & Proportional Relationships The Number System Expressions & Equations Geometry Statistics & Probability Introduction The Number System Expressions & Equations Functions Geometry Statistics & Probability Introduction The Real Number System Quantities* The Complex Number System Vector & Matrix Quantities Introduction Seeing Structure in Expressions Arithmetic with Polynomials & Rational Expressions Creating Equations* Reasoning with Equations & Inequalities Introduction Interpreting Functions Building Functions Linear, Quadratic, & Exponential Models* Trigonometric Functions High School: Modeling Introduction Congruence Similarity, Right Triangles, & Trigonometry Circles Expressing Geometric Properties with Equations Geometric Measurement & Dimension Modeling with Geometry Introduction Interpreting Categorical & Quantitative Data Making Inferences & Justifying Conclusions Conditional Probability & the Rules of Probability Using Probability to Make Decisions Courses & Transitions Mathematics Glossary Table 1 Table 2 Table 3 Table 4 Table 5 Counting & Cardinality Operations & Algebraic Thinking Number & Operations in Base Ten Number & Operations—Fractions Measurement & Data Geometry Ratios & Proportional Relationships The Number System Expressions & Equations Functions Statistics & Probability Mathematics Appendix A Sitemap Contact Terms of Use Public License Developers & Publishers © 2017 Common Core State Standards Initiative