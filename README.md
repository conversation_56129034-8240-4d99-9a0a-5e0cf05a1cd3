# Natural Language Processing
This REPO contains the Natural Language Processing (NLP) projects. 



# Install 
These projects utilize and require downloading Python and NTLK data

Code for NTLK download:  
<pre> # Python Kernal 

  import nltk
  nltk.download()
 
 # GUI opens, download all. 
</pre>

# Project files: 
 **utils.py** - Functions written and utilized throughout the course to create our own NLP  library.  
 **lectures.py** - Scratchpad for lectures  
 **HW_1.py** - A file to familiarize myself with basic functionality of Python  
 **/Data** - Datasets downloaded and utilized across all projects in this file  
 **/Outputs** - Output folder for specfic functions and project datasheets 



